package com.ximalaya.ting.android.opensdk.model.advertis;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @Time 2022/1/25
 * @Description adx接口返回服务透传字段：
 */
public class BusinessExtraInfo implements Parcelable {
    /*
    {
        "businessExtraInfo":{
             //弹窗提示样式,0-不出弹窗提示，1-弹窗提示样式1，2-弹窗提示样式2
            "popReminderStyle":"1",
             //弹窗提示文本
            "popReminderText":"1"
            //内容推广落地页跳转声音
        "jumpTrackId":123,
        //内容推广落地页自动跳转时间
        "autoJumpTime":10,
        // 0-广告列表，1-取top1直接跳转
        soundAggType
        // app 合规信息
         "brandAppManageData": "{\"downloadProgressBarClickType\":2,\"id\":0}",
         // app 弹窗配置
      "downloadPopupStyle": "{\"downloadPopUpClickArea\":1,\"downloadPopUpType\":3,\"enableDownloadPopUp\":true}"
        }
        autoScroll;//是否自动拉起落地页，ab实验控制
        dpAutoJumpSecond;//n秒后自动跳转dp/小程序，服务端配置控制
         private int dpAutoJumpShowSecond;//跳转显示的秒数，服务端配置控制
         private int scrollClickReport; // 自动拉起是否上报
        "appPermissionUrl" ：权限字段
    }
     */

    // 弹窗提示样式,0-不出弹窗提示，1-弹窗提示样式1，2-弹窗提示样式2
    private int popReminderStyle;
    // 弹窗提示文本
    private String popReminderText;

    /**
     * 内容推广落地页
     */
    private long jumpTrackId; // adx 透传给主站时使用该字段
    private int autoJumpTime; //adx 透传给主站时使用该字段
    private int autoPopUpSecond;//ads 内部安装完成后，是否弹窗引导打开

    /**
     * 播放页彩蛋跳转字段
     * 0-广告列表，1-取top1直接跳转
     */
    private String soundAggType;

    private String brandAppManageData;

    private String downloadPopupStyle;

    private boolean autoScroll;//是否自动拉起落地页，ab实验控制

    private int dpAutoJumpSecond;//n秒后自动跳转dp/小程序，服务端配置控制

    private int dpAutoJumpShowSecond;//n秒后自动跳转dp/小程序，服务端配置控制

    private boolean scrollClickReport; //  自动拉起是否上报

    private boolean enableShowAppInfo;// 是否需要悬浮展示下载信息至物料上

    private String appPermissionUrl; // 权限链接

    private String commonReportMap; // adx 透传 字段

    private String appIntroductionUrl; // 介绍h5链接
    private String appIntroductionText; // 介绍h5文字

    private String ubtReportMap; // ubt 透传字段

    private String clickPostMap; // ubt 点击post上报方式放在body中的字段
    private String clickRequestMethod; // ubt 点击上报方式 ，枚举类型 POST/GET

    private String trueExposure;

    private int reportClickTime;
    private boolean bannerMiniProgram; // 是否是小程序任务

    private IncentivePopupInfo incentivePopupInfo;

    public BusinessExtraInfo() {
    }

    public int getPopReminderStyle() {
        return popReminderStyle;
    }

    public void setPopReminderStyle(int popReminderStyle) {
        this.popReminderStyle = popReminderStyle;
    }

    public String getPopReminderText() {
        return popReminderText;
    }

    public void setPopReminderText(String popReminderText) {
        this.popReminderText = popReminderText;
    }

    public long getJumpTrackId() {
        return jumpTrackId;
    }

    public void setJumpTrackId(long jumpTrackId) {
        this.jumpTrackId = jumpTrackId;
    }

    public int getAutoJumpTime() {
        return autoJumpTime;
    }

    public void setAutoJumpTime(int autoJumpTime) {
        this.autoJumpTime = autoJumpTime;
    }


    public String getSoundAggType() {
        return soundAggType;
    }

    public void setSoundAggType(String soundAggType) {
        this.soundAggType = soundAggType;
    }

    public String getBrandAppManageData() {
        return brandAppManageData;
    }

    public void setBrandAppManageData(String brandAppManageData) {
        this.brandAppManageData = brandAppManageData;
    }

    public String getDownloadPopupStyle() {
        return downloadPopupStyle;
    }

    public void setDownloadPopupStyle(String downloadPopupStyle) {
        this.downloadPopupStyle = downloadPopupStyle;
    }

    public boolean isAutoScroll() {
        return autoScroll;
    }

    public void setAutoScroll(boolean autoScroll) {
        this.autoScroll = autoScroll;
    }

    public int getDpAutoJumpSecond() {
        return dpAutoJumpSecond;
    }

    public void setDpAutoJumpSecond(int dpAutoJumpSecond) {
        this.dpAutoJumpSecond = dpAutoJumpSecond;
    }

    public int getDpAutoJumpShowSecond() {
        return dpAutoJumpShowSecond;
    }

    public void setDpAutoJumpShowSecond(int dpAutoJumpShowSecond) {
        this.dpAutoJumpShowSecond = dpAutoJumpShowSecond;
    }

    public boolean isScrollClickReport() {
        return scrollClickReport;
    }

    public void setScrollClickReport(boolean scrollClickReport) {
        this.scrollClickReport = scrollClickReport;
    }

    public boolean isEnableShowAppInfo() {
        return enableShowAppInfo;
    }

    public void setEnableShowAppInfo(boolean enableShowAppInfo) {
        this.enableShowAppInfo = enableShowAppInfo;
    }

    public String getAppPermissionUrl() {
        return appPermissionUrl;
    }

    public void setAppPermissionUrl(String appPermissionUrl) {
        this.appPermissionUrl = appPermissionUrl;
    }
    public int getAutoPopUpSecond() {
        return autoPopUpSecond;
    }

    public void setAutoPopUpSecond(int autoPopUpSecond) {
        this.autoPopUpSecond = autoPopUpSecond;
    }

    public String getCommonReportMap() {
        return commonReportMap;
    }

    public void setCommonReportMap(String commonReportMap) {
        this.commonReportMap = commonReportMap;
    }

    public String getAppIntroductionUrl() {
        return appIntroductionUrl;
    }

    public void setAppIntroductionUrl(String appIntroductionUrl) {
        this.appIntroductionUrl = appIntroductionUrl;
    }

    public String getAppIntroductionText() {
        return appIntroductionText;
    }

    public void setAppIntroductionText(String appIntroductionText) {
        this.appIntroductionText = appIntroductionText;
    }

    public String getUbtReportMap() {
        return ubtReportMap;
    }

    public void setUbtReportMap(String ubtReportMap) {
        this.ubtReportMap = ubtReportMap;
    }

    public String getClickPostMap() {
        return clickPostMap;
    }

    public void setClickPostMap(String clickPostMap) {
        this.clickPostMap = clickPostMap;
    }

    public String getClickRequestMethod() {
        return clickRequestMethod;
    }

    public void setClickRequestMethod(String clickRequestMethod) {
        this.clickRequestMethod = clickRequestMethod;
    }

    public boolean getTrueExposure() {
        return ("true".equals(trueExposure));
    }

    public void setTrueExposure(String trueExposure) {
        this.trueExposure = trueExposure;
    }


    public void setReportClickTime(int reportClickTime) {
        this.reportClickTime = reportClickTime;
    }

    public int getReportClickTime() {
        return reportClickTime;
    }

    public boolean isBannerMiniProgram() {
        return bannerMiniProgram;
    }

    public void setBannerMiniProgram(boolean bannerMiniProgram) {
        this.bannerMiniProgram = bannerMiniProgram;
    }

    public IncentivePopupInfo getIncentivePopupInfo() {
        return incentivePopupInfo;
    }

    public void setIncentivePopupInfo(IncentivePopupInfo incentivePopupInfo) {
        this.incentivePopupInfo = incentivePopupInfo;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.popReminderStyle);
        dest.writeString(this.popReminderText);
        dest.writeLong(this.jumpTrackId);
        dest.writeInt(this.autoJumpTime);
        dest.writeInt(this.autoPopUpSecond);
        dest.writeString(this.soundAggType);
        dest.writeString(this.brandAppManageData);
        dest.writeString(this.downloadPopupStyle);
        dest.writeByte(this.autoScroll ? (byte) 1 : (byte) 0);
        dest.writeInt(this.dpAutoJumpSecond);
        dest.writeInt(this.dpAutoJumpShowSecond);
        dest.writeByte(this.scrollClickReport ? (byte) 1 : (byte) 0);
        dest.writeByte(this.enableShowAppInfo ? (byte) 1 : (byte) 0);
        dest.writeString(this.appPermissionUrl);
        dest.writeString(this.commonReportMap);
        dest.writeString(this.appIntroductionUrl);
        dest.writeString(this.appIntroductionText);
        dest.writeString(this.ubtReportMap);
        dest.writeString(this.clickPostMap);
        dest.writeString(this.clickRequestMethod);
        dest.writeString(this.trueExposure);
        dest.writeInt(this.reportClickTime);
        dest.writeInt(this.bannerMiniProgram ? (byte) 1 : (byte) 0);
        dest.writeParcelable(this.incentivePopupInfo, flags);
    }

    public void readFromParcel(Parcel source) {
        this.popReminderStyle = source.readInt();
        this.popReminderText = source.readString();
        this.jumpTrackId = source.readLong();
        this.autoJumpTime = source.readInt();
        this.autoPopUpSecond = source.readInt();
        this.soundAggType = source.readString();
        this.brandAppManageData = source.readString();
        this.downloadPopupStyle = source.readString();
        this.autoScroll = source.readByte() != 0;
        this.dpAutoJumpSecond = source.readInt();
        this.dpAutoJumpShowSecond = source.readInt();
        this.scrollClickReport = source.readByte() != 0;
        this.enableShowAppInfo = source.readByte() != 0;
        this.appPermissionUrl = source.readString();
        this.commonReportMap = source.readString();
        this.appIntroductionUrl = source.readString();
        this.appIntroductionText = source.readString();
        this.ubtReportMap = source.readString();
        this.clickPostMap = source.readString();
        this.clickRequestMethod = source.readString();
        this.trueExposure = source.readString();
        this.reportClickTime = source.readInt();
        this.bannerMiniProgram = source.readInt() != 0;
        this.incentivePopupInfo = source.readParcelable(IncentivePopupInfo.class.getClassLoader());
    }

    protected BusinessExtraInfo(Parcel in) {
        this.popReminderStyle = in.readInt();
        this.popReminderText = in.readString();
        this.jumpTrackId = in.readLong();
        this.autoJumpTime = in.readInt();
        this.autoPopUpSecond = in.readInt();
        this.soundAggType = in.readString();
        this.brandAppManageData = in.readString();
        this.downloadPopupStyle = in.readString();
        this.autoScroll = in.readByte() != 0;
        this.dpAutoJumpSecond = in.readInt();
        this.dpAutoJumpShowSecond = in.readInt();
        this.scrollClickReport = in.readByte() != 0;
        this.enableShowAppInfo = in.readByte() != 0;
        this.appPermissionUrl = in.readString();
        this.commonReportMap = in.readString();
        this.appIntroductionUrl = in.readString();
        this.appIntroductionText = in.readString();
        this.ubtReportMap = in.readString();
        this.clickPostMap = in.readString();
        this.clickRequestMethod = in.readString();
        this.trueExposure = in.readString();
        this.reportClickTime = in.readInt();
        this.bannerMiniProgram = in.readInt() != 0;
        this.incentivePopupInfo = in.readParcelable(IncentivePopupInfo.class.getClassLoader());
    }

    public static final Creator<BusinessExtraInfo> CREATOR = new Creator<BusinessExtraInfo>() {
        @Override
        public BusinessExtraInfo createFromParcel(Parcel source) {
            return new BusinessExtraInfo(source);
        }

        @Override
        public BusinessExtraInfo[] newArray(int size) {
            return new BusinessExtraInfo[size];
        }
    };

    public static class IncentivePopupInfo implements Parcelable {
        // 弹窗标题
        private String popupTitle;
        // 弹窗描述
        private String popupDescription;
        // 按钮文字
        private String buttonText;
        // 自动关闭延迟（秒）
        private int autoCloseDelay;
        // 弹窗出现延迟（秒）
        private int popupAfterSec;

        public IncentivePopupInfo() {}

        protected IncentivePopupInfo(Parcel in) {
            popupTitle = in.readString();
            popupDescription = in.readString();
            buttonText = in.readString();
            autoCloseDelay = in.readInt();
            popupAfterSec = in.readInt();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(popupTitle);
            dest.writeString(popupDescription);
            dest.writeString(buttonText);
            dest.writeInt(autoCloseDelay);
            dest.writeInt(popupAfterSec);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<IncentivePopupInfo> CREATOR = new Creator<IncentivePopupInfo>() {
            @Override
            public IncentivePopupInfo createFromParcel(Parcel in) {
                return new IncentivePopupInfo(in);
            }

            @Override
            public IncentivePopupInfo[] newArray(int size) {
                return new IncentivePopupInfo[size];
            }
        };

        public String getPopupTitle() { return popupTitle; }
        public void setPopupTitle(String popupTitle) { this.popupTitle = popupTitle; }
        public String getPopupDescription() { return popupDescription; }
        public void setPopupDescription(String popupDescrption) { this.popupDescription = popupDescrption; }
        public String getButtonText() { return buttonText; }
        public void setButtonText(String buttonText) { this.buttonText = buttonText; }
        public int getAutoCloseDelay() { return autoCloseDelay; }
        public void setAutoCloseDelay(int autoCloseDelay) { this.autoCloseDelay = autoCloseDelay; }
        public int getPopupAfterSec() { return popupAfterSec; }
        public void setPopupAfterSec(int popupAfterSec) { this.popupAfterSec = popupAfterSec; }
    }

}
