package com.ximalaya.ting.android.reactnative.modules;

import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_CASH_RECEIVE;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_MALL_TASK;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_PLAY_LET_TASK;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.module.annotations.ReactModule;
import com.facebook.react.uimanager.NativeViewHierarchyManager;
import com.facebook.react.uimanager.UIBlock;
import com.facebook.react.uimanager.UIManagerModule;
import com.google.gson.Gson;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.adsdk.external.feedad.IAdModel;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.data.model.ad.AdGoldCoinResponseData;
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog;
import com.ximalaya.ting.android.host.feedback.XmAdFeedbackUtil;
import com.ximalaya.ting.android.host.feedback.XmFeedBackManager;
import com.ximalaya.ting.android.host.feedback.XmFeedBackPopDialog;
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager;
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener;
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build;
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild;
import com.ximalaya.ting.android.host.feedback.model.MoreTitleModel;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.hybrid.utils.MD5Tool;
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback;
import com.ximalaya.ting.android.host.listener.IDataChangeCallback;
import com.ximalaya.ting.android.host.manager.NewShowNotesManager;
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager;
import com.ximalaya.ting.android.host.manager.ToListenManager;
import com.ximalaya.ting.android.host.manager.TrackCollectManager;
import com.ximalaya.ting.android.host.manager.XuidManager;
import com.ximalaya.ting.android.host.manager.account.AnchorCollectManage;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.FindFollowPositionManage;
import com.ximalaya.ting.android.host.manager.account.NoReadManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.RewardCoinAgainAdManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV3;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardGiveUpHintDialog;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.zone.IZoneFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.ShareTraceUtilKt;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.manager.share.panel.SharePanelType;
import com.ximalaya.ting.android.host.manager.statistic.ListenTaskUtil;
import com.ximalaya.ting.android.host.manager.statistic.OneDayListenTimeManager;
import com.ximalaya.ting.android.host.manager.statistic.UserOneDateListenDuration;
import com.ximalaya.ting.android.host.manager.track.AgentRadioEventManage;
import com.ximalaya.ting.android.host.manager.track.AgentRadioParam;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage;
import com.ximalaya.ting.android.host.model.XmFeedInnerModel;
import com.ximalaya.ting.android.host.model.ad.AdCollectDataRewardVideo;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.IncentiveRewardResponse;
import com.ximalaya.ting.android.host.model.ad.JssdkFuliSuperCommonModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.model.ad.ShareAdRequestParams;
import com.ximalaya.ting.android.host.model.album.AlbumCollectParam;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.feed.VideoInfoBean;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.model.user.NoReadModel;
import com.ximalaya.ting.android.host.util.GsonUtils;
import com.ximalaya.ting.android.host.util.KachaRedDotUtil;
import com.ximalaya.ting.android.host.util.ObjectUploaderUtil;
import com.ximalaya.ting.android.host.util.calendar.CalendarOpProvider;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.other.SoundVideoMuxUtil;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.util.MyListenAbUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.track.SimpleTrackForToListen;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.reactnative.modules.ad.RnAdTrace;
import com.ximalaya.ting.android.reactnative.modules.business.ReactViewDataHolder;
import com.ximalaya.ting.android.reactnative.utils.RNUtils;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.shareservice.AbstractShareType;
import com.ximalaya.ting.android.upload.ObjectUploadManager;
import com.ximalaya.ting.android.upload.common.UploadType;
import com.ximalaya.ting.android.upload.listener.IObjectUploadListener;
import com.ximalaya.ting.android.upload.model.IToUploadObject;
import com.ximalaya.ting.android.upload.model.ToUploadObject;
import com.ximalaya.ting.android.upload.model.UploadItem;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.MD5;
import com.ximalaya.ting.android.xmutil.NetworkType;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by Travis on 2021/3/8 10:40 AM.
 *
 * <AUTHOR>
 */

@ReactModule(name = BusinessModule.NAME)
public class BusinessModule extends ReactContextBaseJavaModule {
    public static final String NAME = "Business";
    //激励视频播放完成
    private static String KEY = "0ab3443b71834b33be675af744d76ef0aaa74cd2b60c4b67";
    private static int CLIENT_CODE_AD_VIDEO_PLAY_COMPLETE = 10003;
    private static int CLIENT_CODE_SHOW = 1;//展示
    private static int CLIENT_CODE_STSRT_PLAY = 2;//开播
    private static int CLIENT_CODE_CLICK = 3;//点击
    private static int CLIENT_CODE_CPMPLETE_PLAY = 4;//完播
    private static int CLIENT_CODE_CLOSE = 5;//关闭
    private static int CLIENT_CODE_REQUEST_REWARD = CLIENT_CODE_AD_VIDEO_PLAY_COMPLETE;//请求发奖励
    private static int CLIENT_CODE_REQUEST_REWARD_FALL_BACK = 10004; // 异常兜底发放奖励
    private static int CLIENT_CODE_ERROR = 7;//错误
    private static Map<Integer,String> EVENT_MAP = new HashMap<>();
    static {
        EVENT_MAP.put(CLIENT_CODE_SHOW,"SHOW");
        EVENT_MAP.put(CLIENT_CODE_CLICK,"CLICK");
        EVENT_MAP.put(CLIENT_CODE_STSRT_PLAY,"PLAY_START");
        EVENT_MAP.put(CLIENT_CODE_CPMPLETE_PLAY,"PLAY_COMPLETE");
        EVENT_MAP.put(CLIENT_CODE_REQUEST_REWARD,"REWARD");
        EVENT_MAP.put(CLIENT_CODE_REQUEST_REWARD_FALL_BACK,"fallBackReq");
        EVENT_MAP.put(CLIENT_CODE_CLOSE,"CLOSE");
    }

    private Handler mHandler = new Handler(Looper.getMainLooper());

    private ObjectUploadManager objectUploadManager;

    public BusinessModule(@Nullable ReactApplicationContext reactApplicationContext) {
        super(reactApplicationContext);
    }

    @NonNull
    @Override
    public String getName() {
        return NAME;
    }

    @ReactMethod
    public void selectHotspotChannel(String channelName, int position, final Promise promise) {
//        android.util.Log.d("z_test_rn", "selectHotspotChannel: " + channelName + ", " + position);
        if (getReactApplicationContext() == null) {
            return;
        }

        Intent intent = new Intent();
        intent.setAction("ACTION_XIMALAYA_ANDROID_SELECT_HOTSPOT_CHANNEL");
        intent.putExtra("channelName", channelName);
        intent.putExtra("position", position);
        LocalBroadcastManager.getInstance(getReactApplicationContext()).sendBroadcast(intent);
    }

    @ReactMethod
    public void setHotConfirmeAction(boolean enable, int selectHour, int selectMinute, Promise promise) {
        if (getReactApplicationContext() == null) {
            return;
        }
        Intent intent = new Intent();
        intent.setAction("ACTION_XIMALAYA_ANDROID_HOT_CONFIRME_ACTION");
        intent.putExtra("enable", enable);
        intent.putExtra("selectHour", selectHour);
        intent.putExtra("selectMinute", selectMinute);
        LocalBroadcastManager.getInstance(getReactApplicationContext()).sendBroadcast(intent);
    }

    @ReactMethod
    public void closeDialog(String dialogName, final Promise promise) {
//        android.util.Log.d("z_test_rn", "closeDialog: " + dialogName);
        if (getReactApplicationContext() == null) {
            return;
        }

        Intent intent = new Intent();
        intent.setAction("ACTION_XIMALAYA_ANDROID_CLOSE_DIALOG");
        intent.putExtra("dialogName", dialogName);
        LocalBroadcastManager.getInstance(getReactApplicationContext()).sendBroadcast(intent);

        Intent intent2 = new Intent();
        intent2.setAction("ACTION_XIMALAYA_ANDROID_CLOSE_DIALOG_GLOBAL");
        intent2.putExtra("dialogName", dialogName);
        LocalBroadcastManager.getInstance(getReactApplicationContext()).sendBroadcast(intent2);
    }

    @ReactMethod
    public void getCurrentDeviceBridge(Promise promise) {
        try {
            String deviceName = Build.MODEL;
            if (TextUtils.isEmpty(deviceName)) {
                deviceName = "android";
            }

            JSONObject result = new JSONObject();
            result.put("deviceName", deviceName);
            result.put("isBlueth", false);
            promise.resolve(RNUtils.jsonToReact(result));
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject(Boolean.FALSE.toString(), "device error");
        }
    }

    @ReactMethod
    public void calendarWrite(ReadableMap data, final Promise promise) {
        if (data == null) {
            promise.reject(Boolean.FALSE.toString(), "data is empty");
            return;
        }

        CalendarOpProvider.debugLog("calendarWrite called: " + data);

        JSONObject args = null;
        try {
            args = RNUtils.reactToJSON(data);
        } catch (Exception e) {
            e.printStackTrace();
            CalendarOpProvider.debugLog("calendarWrite failed, reactToJSON error: " + e.getMessage());
        }

        if (args == null) {
            promise.reject(Boolean.FALSE.toString(), "参数解析失败");
            return;
        }

        final Activity topActivity = getCurrentActivity();
        if (!(topActivity instanceof FragmentActivity)) {
            promise.reject(Boolean.FALSE.toString(), "activity error");
            return;
        }

        CalendarOpProvider.checkPermissionAndWrite((FragmentActivity) topActivity, args, new CalendarOpProvider.Callback() {
            @Override
            public void callback(JSONObject data) {
                CalendarOpProvider.debugLog("callback in RN, data: " + data);

                try {
                    promise.resolve(RNUtils.jsonToReact(data));
                } catch (Exception e) {
                    promise.reject(Boolean.FALSE.toString(), "json to object error: " + data.toString());
                }
            }
        });
    }

    @ReactMethod
    public void trackLike(final ReadableMap data, final Promise promise) {
        if (data == null) {
            promise.reject(Boolean.FALSE.toString(), "data is empty");
            return;
        }

        HandlerManager.postOnMainAuto(new Runnable() {
            @Override
            public void run() {
                double trackId = data.getDouble("trackId");
                boolean isLike = data.getBoolean("isLike");

                Track track = new Track();
                track.setDataId((long) trackId);
                track.setLike(isLike);
                LikeTrackManage.toLikeOrUnLike(track, null, BaseApplication.getTopActivity(), new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(@Nullable Boolean object) {
                        promise.resolve(object != null);
                    }

                    @Override
                    public void onError(int code, String message) {
                        promise.resolve(false);
                        if (!TextUtils.isEmpty(message)) {
                            CustomToast.showFailToast(message);
                        }
                    }
                });
            }
        });
    }

    @ReactMethod
    public void getAgentAudioSupport(Promise promise){
        try {
            promise.resolve("1");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void albumFavorite(final ReadableMap data, final Promise promise) {
        if (data == null) {
            promise.reject(Boolean.FALSE.toString(), "data is empty");
            return;
        }

        HandlerManager.postOnMainAuto(new Runnable() {
            @Override
            public void run() {
                try {
                    String subscribeBizType = RNUtils.optStringFromRMap(data, "subscribeBizType");
                    String followBizType = RNUtils.optStringFromRMap(data, "followBizType");
                    final String followSubBizType = RNUtils.optStringFromRMap(data, "followSubBizType");
                    boolean isFavorite = RNUtils.optBooleanFromRMap(data, "isFavorite", false);
                    double albumId = RNUtils.optDoubleFromRMap(data, "albumId");
                    double anchorId = RNUtils.optDoubleFromRMap(data, "anchorId");
                    long agentAudioId = (long) RNUtils.optDoubleFromRMap(data, "agentAudioId");
                    String agentAudioSource = RNUtils.optStringFromRMap(data, "agentAudioSource");

                    if (agentAudioId > 0 && !TextUtils.isEmpty(agentAudioSource)) {
                        AgentRadioParam param = new AgentRadioParam(agentAudioId, isFavorite ? 1 : 0, agentAudioSource);
                        boolean showToast = RNUtils.optBooleanFromRMap(data, "errorShowToast", true);
                        AgentRadioEventManage.doCollectAction(param, new IDataCallBack<Boolean>() {
                            @Override
                            public void onSuccess(@Nullable Boolean data) {
                                promise.resolve(true);
                            }

                            @Override
                            public void onError(int code, String message) {
                                promise.resolve(false);
                            }
                        }, showToast);
                        return;
                    }

                    AlbumM albumM = new AlbumM();
                    albumM.setFavorite(isFavorite);
                    albumM.setId((long) albumId);
                    albumM.setUid((long) anchorId);
                    try {

                        BaseFragment2 fragment2 = RNUtils.queryFragmentByReactContext(getReactApplicationContext());

                        final int followSubBizTypeInteger = Integer.parseInt(followSubBizType);
                        AlbumCollectParam param = new AlbumCollectParam("QuickListen",
                                Integer.parseInt(subscribeBizType),
                                Integer.parseInt(followBizType),
                                followSubBizTypeInteger,
                                albumM, true);
                        param.setShowFollowDialog(false);
                        param.setUseNewFollowDialog(false);

                        AlbumEventManage.doCollectActionV3(
                                param,
                                fragment2, new ICollectWithFollowStatusCallback() {
                                    @Override
                                    public void followDialogAction(int status) {
                                    }

                                    @Override
                                    public void onCollectSuccess(int code, boolean isCollected) {
                                        promise.resolve(true);
                                    }

                                    @Override
                                    public void onError() {
                                        promise.resolve(false);
                                    }

                                    @Override
                                    public int getFollowSubBizType() {
                                        return followSubBizTypeInteger;
                                    }
                                });
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                    }
                } catch (Exception e) {
                    if (ConstantsOpenSdk.isDebug) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });

    }

    @ReactMethod
    public void listenEarnRewardCoin(ReadableMap data, Promise promise) {
        if (data == null) {
            promise.reject(Boolean.FALSE.toString(), "data is empty");
            return;
        }
        JssdkFuliSuperCommonModel superCommonModel = null;
        try {
            JSONObject object = RNUtils.reactToJSON(data);
            Gson gson = new Gson();
            superCommonModel = gson.fromJson(object.toString(), JssdkFuliSuperCommonModel.class);
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }
        dealWithSupperCommonAdVideoShow(superCommonModel, promise);
    }

    @ReactMethod
    public void getUserListenData(final Promise promise) {
        UserOneDateListenDuration.getDataForJs(getCurrentActivity(), new
                UserOneDateListenDuration.IGetDataCallBack() {
                    @Override
                    public void getData(String data) {
                        try {
                            JSONObject jsonData = new JSONObject();
                            jsonData.put("onDayListenTime", com.ximalaya.ting.android.framework.arouter.utils.TextUtils.isEmpty(data) ? "" : new JSONObject(data));

                            try {
                                jsonData.put("dailyListeningTask", ListenTaskUtil.getInstance().listenDateForJs(getCurrentActivity()));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            try {
                                jsonData.put("listenData", OneDayListenTimeManager.INSTANCE.listenDateForJs());
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                            promise.resolve(RNUtils.jsonToReact(jsonData));
                        } catch (Exception e) {
                            e.printStackTrace();
                            promise.reject(Boolean.FALSE.toString(), e.getMessage());
                        }
                    }
                });
    }

    @ReactMethod
    public void getVideoAntiUrl(ReadableMap data, final Promise promise) {
        if (data == null || !data.hasKey("feedId")) {
            promise.reject(Boolean.FALSE.toString(), "feedId is empty");
            return;
        }
        int feedId = data.getInt("feedId");
        Map<String, String> videoInfoParams = new HashMap<>();
        videoInfoParams.put("feedId", feedId + "");
        videoInfoParams.put(HttpParamsConstants.PARAM_DEVICE, "android");
        CommonRequestM.getDynamicVideoInfo(videoInfoParams, new IDataCallBack<VideoInfoBean>() {
            @Override
            public void onSuccess(@Nullable VideoInfoBean object) {
                if (object == null) {
                    promise.reject(Boolean.FALSE.toString(), "netwrok error");
                    return;
                }
                String realUrl = object.getRealUrl();
                if (TextUtils.isEmpty(realUrl)) {
                    realUrl = ToolUtil.getVideoRealUrl(object);
                    if (TextUtils.isEmpty(realUrl)) {
                        promise.reject(Boolean.FALSE.toString(), "netwrok error");
                        return;
                    }
                }
                try {
                    JSONObject result = new JSONObject();
                    result.put("url", realUrl);
                    promise.resolve(RNUtils.jsonToReact(result));
                } catch (Exception e) {
                    e.printStackTrace();
                    promise.reject(Boolean.FALSE.toString(), "netwrok error");
                }
            }

            @Override
            public void onError(int code, String message) {
                promise.reject(Boolean.FALSE.toString(), "netwrok error");
            }
        });
    }

    /**
     * 通用激励视频
     */
    private void dealWithSupperCommonAdVideoShow(final JssdkFuliSuperCommonModel superCommonModel, final Promise promise) {
        //positionName: 'name', // 业务名称 （产品给）
        //slot_id: '945063602' // 广告位id（产品给）
        //数据校验
        if (superCommonModel == null || TextUtils.isEmpty(superCommonModel.slot_id) || TextUtils.isEmpty(superCommonModel.positionName)) {
            promise.reject(Boolean.FALSE.toString(), "data error");
            return;
        }

        if (!NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
            promise.reject(Boolean.FALSE.toString(), "network error");
            return;
        }

        final Activity topActivity = getCurrentActivity();
        if (!(topActivity instanceof FragmentActivity)) {
            promise.reject(Boolean.FALSE.toString(), "activity error");
            return;
        }

        final RewardExtraParams rewardExtraParams = new RewardExtraParams();

        JSONObject json =
                ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_FORWARD_VIDEO_CONFIG);
        boolean watchVideoClosenable = true;

        int watchVideoTime = RewardExtraParams.DEFAULT_CLOSE_TIME;
        if (json != null) {
            watchVideoClosenable = json.optBoolean("watchVideoClosenable", true);
            watchVideoTime = json.optInt("watchVideoTime", RewardExtraParams.DEFAULT_CLOSE_TIME);
        }
        rewardExtraParams.setCloseable(watchVideoClosenable);
        rewardExtraParams.setCanCloseTime(watchVideoTime);

        final int adSource = Advertis.AD_SOURCE_CSJ;
        mHandler.post(new Runnable() {
            @Override
            public void run() {

                RewardVideoAdManager.getInstance().loadRewardAd(topActivity, superCommonModel.slot_id,
                        adSource, rewardExtraParams, new IVideoAdStatueCallBack() {
                            boolean isPlaying;
                            Dialog mDialog;
                            boolean isComplete = false;
                            boolean isClosed = false;

                            @Override
                            public void onAdLoad(AbstractThirdAd thirdAd) {
                                isPlaying = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).isPlaying();

                                if (isPlaying) {
                                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).pause(PauseReason.Business.RnBusinessModule);
                                }

                                AdCollectDataRewardVideo adCollectDataShowTime = new AdCollectDataRewardVideo();
                                adCollectDataShowTime.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                                adCollectDataShowTime.setPositionName(superCommonModel.positionName);
                                adCollectDataShowTime.setDspPositionId(superCommonModel.slot_id);
                                adCollectDataShowTime.setSdkType(AdManager.getSDKType(adSource) + "");
                                adCollectDataShowTime.setObType("1");
                                CommonRequestM.statOnlineAd(adCollectDataShowTime);
                            }

                            @Override
                            public void onAdLoadError(int code, String message) {
                                promise.reject(Boolean.FALSE.toString(), message);
                            }

                            @Override
                            public void onAdPlayStart() {

                            }

                            @Override
                            public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
                                AdCollectDataRewardVideo adCollectDataShowTime = new AdCollectDataRewardVideo();
                                adCollectDataShowTime.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                                adCollectDataShowTime.setPositionName(superCommonModel.positionName);
                                adCollectDataShowTime.setDspPositionId(superCommonModel.slot_id);
                                adCollectDataShowTime.setSdkType(AdManager.getSDKType(adSource) + "");
                                adCollectDataShowTime.setObType("3");
                                CommonRequestM.statOnlineAd(adCollectDataShowTime);
                            }

                            @Override
                            public void onAdClose(boolean isCustomCloseBtn) {
                                if (isPlaying) {
                                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).play();
                                }

                                if (isCustomCloseBtn) {
                                    promise.reject(Boolean.FALSE.toString(), "custom close");
                                } else if (isComplete) {
                                    isClosed = true;

                                    JSONObject successData = new JSONObject();
                                    try {
                                        successData.put("clientCode", CLIENT_CODE_AD_VIDEO_PLAY_COMPLETE);
                                        promise.resolve(RNUtils.jsonToReact(successData));
                                    } catch (JSONException e) {
                                        e.printStackTrace();
                                        promise.reject(Boolean.FALSE.toString(), "result data error");
                                    }
                                } else {
                                    promise.reject(Boolean.FALSE.toString(), "not complete");
                                }
                            }

                            @Override
                            public void onAdPlayComplete() {
                                isComplete = true;

                                if (mDialog != null) {
                                    mDialog.dismiss();
                                    mDialog = null;
                                }

                                AdCollectDataRewardVideo adCollectDataShowTime = new AdCollectDataRewardVideo();
                                adCollectDataShowTime.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                                adCollectDataShowTime.setPositionName(superCommonModel.positionName);
                                adCollectDataShowTime.setDspPositionId(superCommonModel.slot_id);
                                adCollectDataShowTime.setSdkType(AdManager.getSDKType(adSource) + "");
                                adCollectDataShowTime.setObType("2");
                                CommonRequestM.statOnlineAd(adCollectDataShowTime);
                            }

                            @Override
                            public void onAdPlayError(int code, String message) {
                                promise.reject(Boolean.FALSE.toString(), "ad play error");
                            }

                            @Override
                            public View.OnClickListener getCloseClickListener(final Activity myActivity) {
                                return new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        if (!ToolUtil.activityIsValid(myActivity)) {
                                            promise.reject(Boolean.FALSE.toString(), "close button click error");
                                            return;
                                        }

                                        RewardGiveUpHintDialog rewardGiveUpHintDialog = new RewardGiveUpHintDialog(myActivity);
                                        rewardGiveUpHintDialog.setShowStyle(RewardGiveUpHintDialog.SHOW_STYLE_TASK_CENTER);
                                        rewardGiveUpHintDialog.setCancleHandle(new IHandleOk() {
                                            @Override
                                            public void onReady() {
                                                onAdClose(true);
                                                myActivity.finish();
                                            }
                                        });
                                        rewardGiveUpHintDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                                            @Override
                                            public void onDismiss(DialogInterface dialog) {
                                                mDialog = null;
                                            }
                                        });
                                        rewardGiveUpHintDialog.show();
                                        mDialog = rewardGiveUpHintDialog;
                                    }
                                };
                            }
                        });
            }
        });
    }

    @ReactMethod
    public void openReport(ReadableMap params, final Promise promise) {
        if (params == null) {
            promise.reject(Boolean.FALSE.toString(), "params is null");
            return;
        }
        final Activity activity = getCurrentActivity();
        try {
            final int type = RNUtils.optIntFromRMap(params, "type");
            final String title = RNUtils.optStringFromRMap(params, "title");
            final long commentId = (long) RNUtils.optDoubleFromRMap(params, "commentId");
            final long uid = (long) RNUtils.optDoubleFromRMap(params, "uid");
            final long feedId = (long) RNUtils.optDoubleFromRMap(params, "feedId");
            final String content = RNUtils.optStringFromRMap(params, "content");
            final String media = RNUtils.optStringFromRMap(params, "media");
            final long commentTime = (long) RNUtils.optDoubleFromRMap(params, "commentTime");


            Router.<MainActionRouter>getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {

                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    try {
                        ArrayList<String> pictureUrl = new ArrayList<>();
                        if (!TextUtils.isEmpty(media)) {
                            try {
                                JSONArray jsonArray = new JSONArray(media);
                                for (int i = 0; i < jsonArray.length(); i++) {

                                    JSONObject node = jsonArray.getJSONObject(i);

                                    int type = node.getInt("type");

                                    if (type == IZoneFunctionAction.EDIT_DATA_TYPE_PIC) {
                                        pictureUrl.add(node.getString("content"));
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        BaseFragment2 report = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newReportFragmentByDynamicComment(feedId, commentId,
                                content, uid, commentTime, pictureUrl);
                        if (activity instanceof MainActivity) {
                            ((MainActivity) activity).startFragment(report);
                            promise.resolve(null);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        promise.reject(e);
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    promise.reject(t);
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                    promise.reject(t);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject(e);
        }


    }


    @ReactMethod
    public void downloadAudioClip(ReadableMap params, final Promise promise) {
        if (params == null) {
            promise.reject(Boolean.FALSE.toString(), "params is null");
            return;
        }
        try {
            final String ext = RNUtils.optStringFromRMap(params, "ext");
            final String url = RNUtils.optStringFromRMap(params, "url");
            final double startTime = RNUtils.optDoubleFromRMap(params, "startTime");
            final double duration = RNUtils.optDoubleFromRMap(params, "duration");
            final File cacheDir = getReactApplicationContext().getExternalCacheDir();
            File videoDir = new File(cacheDir, "rn_video");
            if (!videoDir.exists()) {
                videoDir.mkdirs();
            }
            final String name = MD5Tool.md5(url);

            File videoFile = new File(videoDir, name + "." + ext);


            SoundVideoMuxUtil.downloadClipSound(url, videoFile.getAbsolutePath(), startTime, duration, new SoundVideoMuxUtil.OnSoundVideoMuxListener() {
                @Override
                public void onSuccess(String output) {
                    promise.resolve(output);
                }

                @Override
                public void onFailed(String msg) {
                    promise.reject(Boolean.FALSE.toString(), msg);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject(Boolean.FALSE.toString(), e.getMessage());
        }

    }


    @ReactMethod
    public void generateVideo(ReadableMap params, final Promise promise) {

        if (params == null) {
            promise.reject(Boolean.FALSE.toString(), "params is null");
            return;
        }

        try {
            final ReadableArray audioPaths = params.getArray("audioPaths");
            if (audioPaths == null || audioPaths.size() <= 0) {
                promise.reject(Boolean.FALSE.toString(), "audioPaths is empty");
                return;
            }
            final String videoPath = RNUtils.optStringFromRMap(params, "videoPath");
            if (TextUtils.isEmpty(videoPath)) {
                promise.reject(Boolean.FALSE.toString(), "videoPath is empty");
                return;
            }
            String[] isp = new String[audioPaths.size()];
            for (int i = 0; i < audioPaths.size(); i++) {
                isp[i] = audioPaths.getString(i);
            }
            SoundVideoMuxUtil.mux(isp, videoPath, new SoundVideoMuxUtil.OnSoundVideoMuxListener() {
                @Override
                public void onSuccess(String output) {
                    promise.resolve(output);
                }

                @Override
                public void onFailed(String msg) {
                    promise.reject(Boolean.FALSE.toString(), msg);
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            promise.reject(Boolean.FALSE.toString(), e.getMessage());
        }

    }

    @ReactMethod
    public void restoreToMainPlayer() {
        try {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    PlayTools.restoreToMainPlayer(MainApplication.getInstance().realApplication);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ReactMethod
    public void getTrackAntiUrl(ReadableMap params, final Promise promise) {
        if (params == null) {
            promise.reject(Boolean.FALSE.toString(), "params is null");
            return;
        }
        try {
            final long trackId = (long) RNUtils.optDoubleFromRMap(params, "trackId");
            final int quality = RNUtils.optIntFromRMap(params, "quality");
            if (trackId <= 0) {
                promise.reject(Boolean.FALSE.toString(), "trackId is null");
                return;
            }
            Map<String, String> secParams = new HashMap<>();
            secParams.put("quality", String.valueOf(quality));
            secParams.put("device", "android");
            CommonRequestM.getTrackForPlayUrl(trackId, secParams, new IDataCallBack<String>() {
                @Override
                public void onSuccess(String object) {
                    if (!TextUtils.isEmpty(object)) {
                        promise.resolve(object);
                    } else {
                        promise.reject(Boolean.FALSE.toString(), "empty");
                    }
                }

                @Override
                public void onError(int code, String message) {
                    promise.reject(Boolean.FALSE.toString(), message);
                }
            });

        } catch (Exception e) {
            promise.reject(e);
        }
    }

    private ObjectUploadManager getObjectUploadManager() {
        if (objectUploadManager == null) {
            objectUploadManager = ObjectUploaderUtil.getObjectUploadManager(getReactApplicationContext());
        }
        return objectUploadManager;
    }

    @ReactMethod
    public void uploadVideo(ReadableMap params, final Promise promise) {
        if (params == null) {
            promise.reject(Boolean.FALSE.toString(), "params is null");
            return;
        }
        try {
            final String filePath = RNUtils.optStringFromRMap(params, "filePath");
            final String uploadType = RNUtils.optStringFromRMap(params, "uploadType");

            final ToUploadVideo objectToUpload = new ToUploadVideo();
            objectToUpload.addVideoInfo(filePath);
            final ObjectUploadManager objectUploadManager = getObjectUploadManager();
            objectUploadManager.upload(objectToUpload);
            objectUploadManager.addUploadListener(new IObjectUploadListener() {
                @Override
                public void onUploadStart(IToUploadObject iToUploadObject) {

                }

                @Override
                public void onUploadProgress(IToUploadObject iToUploadObject, int i) {

                }

                @Override
                public void onUploadFinish(IToUploadObject iToUploadObject) {
                    objectUploadManager.removeUploadListener(this);
                    if (iToUploadObject instanceof ToUploadVideo
                            && iToUploadObject.getUploadItems() != null
                            && iToUploadObject.getUploadItems().size() > 0
                            && iToUploadObject.getUploadItems().get(0) != null) {
                        final String fileUrl = iToUploadObject.getUploadItems().get(0).getFileUrl();
                        if (!TextUtils.isEmpty(fileUrl)) {
                            promise.resolve(fileUrl);
                        } else {
                            promise.reject(Boolean.FALSE.toString(), "fileUrl is empty");
                        }
                    }
                }

                @Override
                public void onUploadError(IToUploadObject iToUploadObject, int i, String s) {
                    objectUploadManager.removeUploadListener(this);
                    if (promise != null) {
                        promise.reject(Boolean.FALSE.toString(), s);
                    }

                }

                @Override
                public void onItemUploadFinish(IToUploadObject iToUploadObject, UploadItem uploadItem) {

                }
            });
        } catch (Exception e) {
            promise.reject(e);
        }
    }

    public static class ToUploadVideo extends ToUploadObject {
        public void addVideoInfo(String videoPath) {
            addUploadItem(new UploadItem(videoPath, UploadType.video.getName(), "", "ting"));
        }

    }



    @ReactMethod
    public void listenEarnRewardCoinNew(ReadableMap data, Promise promise) {
        Logger.i("msg_video_forward", "  ---- rn 调起 激励视频 --> 参数data " + data);
        RnAdTrace.traceEvent(null, "收到rn调用");
        if (data == null) {
            promise.reject(Boolean.FALSE.toString(), "data is empty");
            RnAdTrace.traceEvent(null, "data is empty");
            return;
        }
        JssdkFuliSuperCommonModel superCommonModel = null;
        try {
            JSONObject object = RNUtils.reactToJSON(data);
//            JSONObject object = new JSONObject("{\"positionName\":\"integral_center_inspire_video\",\"slot_id\":254,\"renderTemplate\":1,\"extInfo\":\"{\\\"aid\\\":161,\\\"taskId\\\":274}\",\"rewardVideoStyle\":1}");
            Gson gson = new Gson();
            superCommonModel = gson.fromJson(object.toString(), JssdkFuliSuperCommonModel.class);
        } catch (Exception ignored) {
            ignored.printStackTrace();
        }
        Logger.i("msg_video_forward", " rn 解析后参数 - superCommonModel = " + superCommonModel);
        dealWithSupperCommonAdVideoShowNew(superCommonModel, promise);
    }

    private void dealWithSupperCommonAdVideoShowNew(final JssdkFuliSuperCommonModel superCommonModel, final Promise promise) {
        //positionName: 'name', // 业务名称 （产品给）
        //slot_id: '945063602' // 广告位id（产品给）
        //数据校验
        Logger.d("msg_video_forward", "  开始准备播放激励视频 -- dealWithSupperCommonAdVideoShowNew ----- -- ---- - -- -- -");
        if (superCommonModel == null || TextUtils.isEmpty(superCommonModel.slot_id) || TextUtils.isEmpty(superCommonModel.positionName)) {
            Logger.e("msg_video_forward", "   ----- -- ---- - -- -- - data error");
            promise.reject(Boolean.FALSE.toString(), "data error");
            RnAdTrace.traceEvent(null, "data error");
            return;
        }

        if (!NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
            Logger.e("msg_video_forward", "   ----- -- ---- - -- -- - network error");
            promise.reject(Boolean.FALSE.toString(), "network error");
            RnAdTrace.traceEvent(null, "network error");
            return;
        }

        final Activity topActivity = getCurrentActivity();
        if (!(topActivity instanceof FragmentActivity)) {
            Logger.e("msg_video_forward", "   ----- -- ---- - -- -- - activity error");
            promise.reject(Boolean.FALSE.toString(), "activity error");
            RnAdTrace.traceEvent(null, "activity error");
            return;
        }


        JSONObject json =
                ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_TASKCENTER_VIDEO_CONFIG);
//        boolean watchVideoClosenable = true;

        int watchVideoTime = RewardExtraParams.DEFAULT_CLOSE_TIME;

        // todo 测试代码
        superCommonModel.positionName = AD_POSITION_NAME_WELFARE_CASH_RECEIVE;

        final String positionName = superCommonModel.positionName;//如果没有PositionId，可以尝试mock一个ID调试
        if (json != null) {
//            watchVideoClosenable = json.optBoolean("watchVideoClosenable", true);
            watchVideoTime = json.optInt("watchVideoTime", RewardExtraParams.DEFAULT_CLOSE_TIME);
        }

        if (TextUtils.isEmpty(positionName)){
            Logger.e("msg_video_forward", "   ----- -- ---- - -- -- - positionName error");
            promise.reject(Boolean.FALSE.toString(), "positionName error");
            RnAdTrace.traceEvent(null, "positionName error");
            return;
        }

        final RewardExtraParams rewardExtraParams = new RewardExtraParams();
        rewardExtraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_POINT);
        //判断是否是福利页领取现金红包广告位，如果是则设置额外参数
        if(isWelfareCash(positionName)){
            buildWelfareCashExtraParams(rewardExtraParams, superCommonModel);
        }
        rewardExtraParams.setPositionName(positionName);
        if (!TextUtils.isEmpty(superCommonModel.configPositionName)) {
            rewardExtraParams.setConfigPositionName(superCommonModel.configPositionName);
        }
        rewardExtraParams.setCanCloseTime(-1);
        Logger.i("msg_video_forward", " 调用sdk内的激励视频 loadRewardAdFromSDK ----- -- ---- - -- -- - ");
        RnAdTrace.traceEvent(null, "调用sdk");
        final String sourceName;
        if (!TextUtils.isEmpty(superCommonModel.sourceName)) {
            // 优先使用rn传的sourceName
            sourceName = superCommonModel.sourceName;
        } else {
            sourceName = isWelfareCash(positionName) ? "welfareSignIn" : "pointCash";
        }
        if (superCommonModel != null && superCommonModel.extInfo != null) {
            rewardExtraParams.setExtInfo(superCommonModel.extInfo);
        }
        if (superCommonModel.rewardVideoStyle == 0) {
            rewardExtraParams.setCanPreloadInspireAd(false);
        }
        AdUnLockTimeManagerV3.getInstance().unlockTrack(PlayerConstants.PLAY_METHOD_MANUAL_PLAY, sourceName, new AdUnLockTimeManagerV3.IAdUnLockStatusCallBack() {
            long startPlayTs, endPlayTs;
            Advertis advertis;
            String adId = "0";

            boolean realFinishTask;
            String responseId = "0";
            int retryCount = 0;

            private long lastRewardWelfareTime;

            @Override
            public void onRewardSuccess(Advertis advertis, boolean finishTask) {
                this.advertis = advertis;
                if (advertis != null) {
                    this.adId = advertis.getAdid() + "";
                    this.responseId = advertis.getResponseId() + "";
                }
                endPlayTs = System.currentTimeMillis();
                realFinishTask = finishTask;
                if (AppConstants.AD_POSITION_NAME_INCENTIVE_WELFARE.equals(positionName)) {
                    callbackForGoldCoin("onReward");
                } else if (isWelfareCash(positionName)) {
                    callbackForWelfare(realFinishTask ? CLIENT_CODE_REQUEST_REWARD : CLIENT_CODE_REQUEST_REWARD_FALL_BACK, "onRewardWelfareCash");
                } else {
                    callback(realFinishTask ? CLIENT_CODE_REQUEST_REWARD : CLIENT_CODE_REQUEST_REWARD_FALL_BACK, "onReward");
                }
            }

            @Override
            public void onRewardFail(String message) {
                if (AD_POSITION_NAME_WELFARE_CASH_RECEIVE.equals(positionName)
                        || AppConstants.AD_POSITION_NAME_INCENTIVE_WELFARE.equals(positionName)
                        || AppConstants.AD_POSITION_NAME_WELFARE_PLAY_LET_TASK.equals(positionName)) {
                    CustomToast.showFailToast(message);
                }
                promise.reject(Boolean.FALSE.toString(), "onRewardFail");
            }

            @Override
            public void onPlayStart() {
                startPlayTs = System.currentTimeMillis();
            }

            private void callback(int code, String message) {
                Logger.i("listenEarnRewardCoinNew", "code=" + code + ",message=" + message);
                if (EVENT_MAP.containsKey(code)) {
                    RNUtils.sendEvent(getReactApplicationContext(), EVENT_MAP.get(code));
                } else {
                    RNUtils.sendEvent(getReactApplicationContext(), "UNKNOW");
                }
                if (code == CLIENT_CODE_REQUEST_REWARD || code == CLIENT_CODE_REQUEST_REWARD_FALL_BACK) {
                    Logger.d("msg_video_forward", "  --- ---- -  callback 了， 要发奖励了 ");
                    if (superCommonModel != null && superCommonModel.rewardVideoStyle == 1) {
                        Logger.w("msg_video_forward", "  --- ---- -  callback 了， 要发奖励了 -- ---- - ads 发奖励 ");
                        String requestId = UUID.randomUUID().toString();
                        onRewardBeforeRequestAds(requestId, code, message, code == CLIENT_CODE_REQUEST_REWARD_FALL_BACK ? "1" : "0");
                    } else {
                        Logger.w("msg_video_forward", "  --- ---- -  callback 了， 请求rn发奖励流程  -- ");
                        onRequestReward(code, message, "rn发奖励 rewardVideoStyle = 0");
                    }
                    Logger.i("listenEarnRewardCoinNew", "CLIENT_CODE_REQUEST_REWARD");
                } else {
                    //TODO 本地处理其他事件上报、日志等操作
                }
            }

            private void callbackForWelfare(int code, String message) {
                Logger.i("listenEarnRewardWelfare", "code=" + code + ",message=" + message);
                if (System.currentTimeMillis() - lastRewardWelfareTime < 1000) {
                    return;
                }
                lastRewardWelfareTime = System.currentTimeMillis();
                if (EVENT_MAP.containsKey(code)) {
                    RNUtils.sendEvent(getReactApplicationContext(), EVENT_MAP.get(code));
                } else {
                    RNUtils.sendEvent(getReactApplicationContext(), "UNKNOW");
                }
                if (code == CLIENT_CODE_REQUEST_REWARD || code == CLIENT_CODE_REQUEST_REWARD_FALL_BACK) {
                    Logger.d("listenEarnRewardWelfare", "  --- ---- -  callback 了， 要发奖励了 ");
                    if (positionName.equals(AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL)) {
                        Logger.w("listenEarnRewardWelfare", "  --- ---- -  callback 了，  通知rn可以提现 ");
                        onRequestReward(code, message, "福利页提取现金流程,通知rn可以提现");
                    } else {
                        Logger.w("listenEarnRewardWelfare", "  --- ---- -  callback 了， 要发奖励了 -- ---- - ads 发奖励 ");
                        //只有完成任务才能发奖励
                        if (code == CLIENT_CODE_REQUEST_REWARD) {
                            String requestId = UUID.randomUUID().toString();
                            try {
                                retryCount = 0;
                                onRewardWelfareRequestAds(requestId, code, message, code == CLIENT_CODE_REQUEST_REWARD_FALL_BACK ? "1" : "0");
                            } catch (Exception e) {
                                Logger.e("listenEarnRewardWelfare", "  --- -- onRewardWelfareRequestAds-- 异常");
                            }
                        }
                    }
                    Logger.i("listenEarnRewardCoinNew", "CLIENT_CODE_REQUEST_REWARD");
                }
            }

            private void callbackForGoldCoin(String message) {
                if (superCommonModel != null && superCommonModel.rewardVideoStyle == 1) {
                    onRewardGoldCoin(message);
                } else {
                    onRequestReward(realFinishTask ? CLIENT_CODE_REQUEST_REWARD : CLIENT_CODE_REQUEST_REWARD_FALL_BACK, message, "走rn发金币奖励");
                }
                Logger.i("listenEarnRewardCoinNew", "CLIENT_CODE_REQUEST_REWARD");
            }

            private void onRewardBeforeRequestAds(final String requestId, final int code, final String message, final String fallbackReq) {
                Logger.d("msg_video_forward", "  ----- onRewardBeforeRequestAds - ads 发奖励- ---- - -- -- - code = " + code + " , msg = " + message);

                Logger.i("msg_video_forward", "  -----  ads 请求 第 " + retryCount + " 次");
                if (retryCount >= 3) {
                    Logger.e("msg_video_forward", "  ----- 已经循环请求了 3次了， 拦截 ads 请求 ");
                    if (promise != null) {
                        promise.reject(Boolean.FALSE.toString(), "reward error");
                    }
                    return;
                }
                long playTime = 30000;  // 默认播放时长
                if (endPlayTs > 0 && startPlayTs > 0) {
                    playTime = endPlayTs - startPlayTs;
                }
                Map<String, String> parmasMap = new HashMap();
                parmasMap.put("requestId", requestId);
                parmasMap.put("responseId", responseId);
                parmasMap.put("adId", adId);
                parmasMap.put("fallbackReq", fallbackReq);
                parmasMap.put("adViewDuration", ((playTime) / 1000) + "");
                parmasMap.put("ts", System.currentTimeMillis() + "");
                String ticketStr = XuidManager.INSTANCE.getTicket("b=incentives&s=gold_reward&u=" + UserInfoMannage.getUid());
                parmasMap.put("ticket", ticketStr);
                String baseSignature = ticketStr + "&" + UserInfoMannage.getUid() + "&" + requestId + "&" + KEY;
                Logger.d("msg_video_forward", "  -----  - ads 发奖励- 加密参数， md5前： baseSignature =  " + baseSignature);
                String signatureStr = MD5.md5(baseSignature);
                parmasMap.put("signature", signatureStr);
                String extInfoStr = "";
                if (superCommonModel != null) {
                    extInfoStr = superCommonModel.extInfo;
                }
                parmasMap.put("extInfo", extInfoStr + "");
                parmasMap.put("retry", retryCount + "");
                retryCount ++;
                Logger.d("msg_video_forward", "  -----  - ads 发奖励- 请求ads 接口参数 --> parasMap =  " + parmasMap);
                AdRequest.getIncentiveReward(parmasMap, new IDataCallBack<String>() {
                    @Override
                    public void onSuccess(@Nullable String response) {
                        Logger.d("msg_video_forward", "  ----- ads 接口返回 -- 发奖励 -- onSuccess ---- rewardResponse = " + response);
                        IncentiveRewardResponse rewardResponse = null;
                        if (!TextUtils.isEmpty(response)) {
                            rewardResponse = new Gson().fromJson(response, IncentiveRewardResponse.class);
                        }

                        if (rewardResponse != null && rewardResponse.getData() != null) {
                            Logger.d("msg_video_forward", "  ----- ads 接口返回 --  发奖励 -- rewardResponse.getData().isSuccess() --->   " + rewardResponse.getData().isSuccess());
                            if (rewardResponse.getData().isSuccess()) {
                                Logger.d("msg_video_forward", "  ----- ads 接口返回 --  发奖励 -- onSuccess --->  发奖  ---- - -- -- - " );
                                onRequestReward(code, message, "新版流程， 请求ads 接口，回调成功后 调用发金币");
                                if (!TextUtils.isEmpty(rewardResponse.getData().getToast())) {
                                    CustomToast.showSuccessToast(rewardResponse.getData().getToast());
                                }
                            } else {
                                Logger.e("msg_video_forward", "  -----  ads 接口返回 --  发奖励 -- 失败了 -- 判断是否需要重新尝试 ---  isRetry = " + rewardResponse.getData().isRetry());
                                if (rewardResponse.getData().isRetry()) {
                                    Logger.e("msg_video_forward", "  -----  ads 接口返回 --  发奖励 -- 失败了 -- 判断是否需要重新尝试 ---  重试请求ads 发奖接口");
                                    onRewardBeforeRequestAds(requestId, code, message, fallbackReq);
                                } else {
                                    if (promise != null) {
                                        promise.reject(Boolean.FALSE.toString(), "onAdReward error");
                                    }
                                }
                            }

                        } else {
                            Logger.e("msg_video_forward", "  -----  ads 接口返回 --  发奖励 -- 失败了 -- rewardResponse = null, 直接回调失败" );
                            if (promise != null) {
                                promise.reject(Boolean.FALSE.toString(), "onAdReward error");
                            }

                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMessage) {
//                        if (promise != null) {
//                            promise.reject(Boolean.FALSE.toString(), "onAdPlayError error");
//                        }
                        Logger.e("msg_video_forward", "  -----  ads 请求 onError --  再次重新请求ads 接口 ---- code = " + code  + " , message = " + message);
                        onRewardBeforeRequestAds(requestId, code, message, fallbackReq);
                    }
                });
            }

            private void onRewardWelfareRequestAds(final String requestId, final int code, final String message, final String fallbackReq) {
                Logger.d("listenEarnRewardWelfare", "  ----- onRewardBeforeRequestAds - ads 发奖励- ---- - -- -- - code = " + code + " , msg = " + message);

                Logger.i("listenEarnRewardWelfare", "  -----  ads 请求 第 " + retryCount + " 次");
                if (retryCount >= 3) {
                    Logger.e("listenEarnRewardWelfare", "  ----- 已经循环请求了 3次了， 拦截 ads 请求 ");
                    if (promise != null) {
                        promise.reject(Boolean.FALSE.toString(), "reward error");
                    }
                    return;
                }

                Map<String, String> parmasMap = new HashMap();
                long ts =  System.currentTimeMillis();
                parmasMap.put("requestId", requestId);
                parmasMap.put("responseId", responseId);
                parmasMap.put("adId", adId);
                parmasMap.put("sourceName", sourceName);
                parmasMap.put("ts",  ts+ "");
                String extInfoStr = "";
                String stepNo = "";
                String rewardTimes = "";
                String ecpm = "";
                if (superCommonModel != null) {
                    extInfoStr = superCommonModel.extInfo;
                    stepNo = superCommonModel.stepNo + "";
                    rewardTimes = rewardExtraParams.getRewardTimes()+"";
                    ecpm = AdUnLockTimeManagerV3.getInstance().getCurAdPriceEncrypt();
                    parmasMap.put("stepNo", stepNo);
                    parmasMap.put("rewardTimes", rewardTimes + "");
                    parmasMap.put("ecpm", ecpm);
                }
                parmasMap.put("extInfo", extInfoStr + "");
                parmasMap.put("encryptType", AdUnLockTimeManagerV3.getInstance().getCurAdPriceEncryptType());
                String ticketStr = XuidManager.INSTANCE.getTicket("b=incentives&s=gold_reward&u=" + UserInfoMannage.getUid());
                parmasMap.put("ticket", ticketStr);
                String deviceId = DeviceUtil.getDeviceToken(MainApplication.getMyApplicationContext());
                String baseSignature = requestId + "&" + deviceId + "&" + ticketStr + "&" + stepNo + "&" + rewardTimes + "&" + ecpm + "&" + ts + "&" + KEY;
                Logger.d("listenEarnRewardWelfare", "  -----  - ads 发奖励- 加密参数， md5前： baseSignature =  " + baseSignature);
                String signatureStr = MD5.md5(baseSignature);
                parmasMap.put("signature", signatureStr);

                parmasMap.put("retry", retryCount + "");
                retryCount ++;
                Logger.d("listenEarnRewardWelfare", "  -----  - ads 发奖励- 请求ads 接口参数 --> parasMap =  " + parmasMap);
                AdRequest.getFreeListenRewardWelfareAward(parmasMap, new IDataCallBack<String>() {
                    @Override
                    public void onSuccess(@Nullable String response) {
                        Logger.d("listenEarnRewardWelfare", "  ----- ads 接口返回 -- 发奖励 -- onSuccess ---- rewardResponse = " + response);
                        IncentiveRewardResponse rewardResponse = null;
                        if (!TextUtils.isEmpty(response)) {
                            rewardResponse = new Gson().fromJson(response, IncentiveRewardResponse.class);
                        }

                        if (rewardResponse != null && rewardResponse.getData() != null) {
                            Logger.d("listenEarnRewardWelfare", "  ----- ads 接口返回 --  发奖励 -- rewardResponse.getData().isSuccess() --->   " + rewardResponse.getData().isSuccess());
                            if (rewardResponse.getData().isSuccess()) {
                                Logger.d("listenEarnRewardWelfare", "  ----- ads 接口返回 --  发奖励 -- onSuccess --->  发奖  ---- - -- -- - " );
                                onRequestReward(code, message, "新版流程， 请求ads 接口，回调成功后 调用发金钱");
                                if (!TextUtils.isEmpty(rewardResponse.getData().getToast())) {
                                    AdUnLockTimeManagerV3.getInstance().showRewardWelfareSuccessPush(topActivity, rewardResponse.getData().getToast());
                                }
                            } else {
                                Logger.e("listenEarnRewardWelfare", "  -----  ads 接口返回 --  发奖励 -- 失败了 -- 判断是否需要重新尝试 ---  isRetry = " + rewardResponse.getData().isRetry());
                                if (rewardResponse.getData().isRetry()) {
                                    Logger.e("listenEarnRewardWelfare", "  -----  ads 接口返回 --  发奖励 -- 失败了 -- 判断是否需要重新尝试 ---  重试请求ads 发奖接口");
                                    onRewardWelfareRequestAds(requestId, code, message, fallbackReq);
                                } else {
                                    if (!TextUtils.isEmpty(rewardResponse.getData().getToast())) {
                                        CustomToast.showFailToast(rewardResponse.getData().getToast());
                                    }
                                    if (promise != null) {
                                        promise.reject(Boolean.FALSE.toString(), "onAdReward error");
                                    }
                                }
                            }

                        } else {
                            Logger.e("listenEarnRewardWelfare", "  -----  ads 接口返回 --  发奖励 -- 失败了 -- rewardResponse = null, 直接回调失败" );
                            if (promise != null) {
                                promise.reject(Boolean.FALSE.toString(), "onAdReward error");
                            }

                        }
                    }

                    @Override
                    public void onError(int errorCode, String errorMessage) {
//                        if (promise != null) {
//                            promise.reject(Boolean.FALSE.toString(), "onAdPlayError error");
//                        }
                        Logger.e("listenEarnRewardWelfare", "  -----  ads 请求 onError --  再次重新请求ads 接口 ---- code = " + code  + " , message = " + message);
                        onRewardWelfareRequestAds(requestId, code, message, fallbackReq);
                    }
                });
            }

            private void onRewardGoldCoin(final String message) {
                String requestId = UUID.randomUUID().toString();
                reportRewardRequest(advertis, "request", true, "", realFinishTask);
                doRewardCoinRequest(superCommonModel.coins, superCommonModel.rewardType, superCommonModel, advertis, null, requestId, 0, realFinishTask,
                        new IRequestRewardCoinCallback() {
                    @Override
                    public void onSuccess(final AdGoldCoinResponseData responseData) {
                        reportRewardRequest(advertis, "result", true, "", realFinishTask);
                        if (responseData == null) {
                            return;
                        }
                        if (RewardCoinAgainAdManager.isNeedRewardAgain() && RewardCoinAgainAdManager.getCurrentInspireAd() != null) {
                            RewardCoinAgainAdManager.showInspireDialog(superCommonModel.coins, responseData.upgradeCoins, responseData.adAgainCoins != 0,
                                    RewardCoinAgainAdManager.getCurrentInspireAd(), true,
                                    new RewardCoinAgainAdManager.IRewardCoinCallback() {
                                        @Override
                                        public void onRewardInspire(IAdModel adModel) {
                                            // 发放唤端奖励的请求
                                            String requestId = UUID.randomUUID().toString();
                                            doRewardCoinRequest(responseData.upgradeCoins, 3, superCommonModel, null, adModel, requestId,0, true, new IRequestRewardCoinCallback() {
                                                @Override
                                                public void onSuccess(AdGoldCoinResponseData responseDataNew) {
                                                    onRequestReward(CLIENT_CODE_REQUEST_REWARD, message, "客户端唤端奖励发放成功");
                                                    RewardCoinAgainAdManager.showSuccessDialog(responseData.upgradeCoins, false, null);
                                                }

                                                @Override
                                                public void onFail(String message) {
                                                    onRequestReward(CLIENT_CODE_REQUEST_REWARD, message, "客户端唤端奖励发放失败");
                                                }
                                            });
                                        }

                                        @Override
                                        public void onRewardFinish() {
                                            onRequestReward(CLIENT_CODE_REQUEST_REWARD, message, "客户端发放奖励结束");
                                        }

                                        @Override
                                        public void onWatchNext() {
                                            superCommonModel.coins = responseData.adAgainCoins;
                                            dealWithSupperCommonAdVideoShowNew(superCommonModel, promise);
                                        }
                                    });
                        } else {
                            // 无唤端广告
                            RewardCoinAgainAdManager.showSuccessDialog(superCommonModel.coins, responseData.adAgainCoins != 0, new RewardCoinAgainAdManager.IRewardCoinCallback() {
                                @Override
                                public void onRewardInspire(IAdModel adModel) {
                                }

                                @Override
                                public void onRewardFinish() {
                                    onRequestReward(CLIENT_CODE_REQUEST_REWARD, message, "客户端发放奖励结束");
                                }

                                @Override
                                public void onWatchNext() {
                                    superCommonModel.coins = responseData.adAgainCoins;
                                    dealWithSupperCommonAdVideoShowNew(superCommonModel, promise);
                                }
                            });
                        }
                    }

                    @Override
                    public void onFail(String message) {
                        reportRewardRequest(advertis, "result", false, message, realFinishTask);
                        if (RewardCoinAgainAdManager.isNeedRewardAgain()) {
                            RewardCoinAgainAdManager.resetInspireAd();
                        }
                        if (promise != null) {
                            promise.reject(Boolean.FALSE.toString(), "reward error");
                        }
                    }
                });
            }

            private void reportRewardRequest(Advertis currentAd, String action, boolean success, String errorMessage, boolean isRealFinishTask) {
                if (currentAd == null || superCommonModel == null) {
                    return;
                }
                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        currentAd, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_REQUEST_REWARD, superCommonModel.positionName)
                                .sdkType(AdManager.getSDKType(currentAd) + "")
                                .dspPositionId(currentAd.getDspPositionId())
                                .uid(UserInfoMannage.getUid() + "")
                                .adUserType(currentAd.getAdUserType())
                                .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                .sourceName(superCommonModel.sourceName)
                                .showStyle(currentAd.getShowstyle() + "")
                                .unlockType(currentAd.getUnlockType())
                                .rewardType(superCommonModel.rewardType + "")
                                .rewardSuccess(true)
                                .action(action)
                                .success(success ? 1 : 0)
                                .errorMsg(errorMessage)
                                .fallBackReq(isRealFinishTask ? 0 : 1)
                                .build());
            }

            //请求前端发放奖励
            private void onRequestReward(int code, String message, String fromStr) {
                Logger.i("msg_video_forward", "  -- -- onRequestReward -  rn 发金币 ----- 来源 = " + fromStr);
                JSONObject successData = new JSONObject();
                try {
                    Logger.i("msg_video_forward", "  -- -- 请求rn发放奖励  000 -  code = " + code + " , msg = " + message);
                    successData.put("clientCode", code);
                    successData.put("clientMsg", message);
                    successData.put("adId", adId);
                    successData.put("adResponseId", responseId);
                    int encryptType = AdManager.getEncryptType(advertis);
                    String ecpm = AdManager.getEcpm(advertis);
                    successData.put("encryptType", encryptType);
                    successData.put("fallbackReq", realFinishTask ? "0" : "1");
                    successData.put("ecpm", ecpm);
                    Logger.i("msg_video_forward", "  -- -- 请求rn发放奖励  111 -  客户端组装参数， successData = " + successData);
                    WritableMap writableMap = RNUtils.jsonToReact(successData);
                    Logger.i("msg_video_forward", "  -- -- 请求rn发放奖励  222 -  回调rn， 参数为writableMap = " + writableMap);
                    promise.resolve(writableMap);
                } catch (JSONException e) {
                    e.printStackTrace();
                    Logger.e("msg_video_forward", "  -- -- 请求rn发放奖励 ， 发放奖励失败 -- error  - code = " + code + " , msg = " + message + "\n " + e);
                    promise.reject(Boolean.FALSE.toString(), "result data error");
                }
            }

        }, rewardExtraParams);

    }

    private void buildWelfareCashExtraParams(RewardExtraParams rewardExtraParams,JssdkFuliSuperCommonModel superCommonModel){
        if (superCommonModel == null) {
            return;
        }
        rewardExtraParams.setRewardCountDownStyle(REWARD_COUNT_DOWN_STYLE_FOR_WELFARE);
        rewardExtraParams.setCashBalance(superCommonModel.cashBalance);
        rewardExtraParams.setMaxRewardTimes(superCommonModel.maxRewardTimes);
        rewardExtraParams.setRewardTimes(superCommonModel.rewardTimes);
    }

    //是否是福利页提取现金广告位
    private boolean isWelfareCash(String positionName) {
        return positionName.equals(AD_POSITION_NAME_WELFARE_CASH_RECEIVE) || positionName.equals(AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL);
    }

    private boolean needNotifyRnReward(String positionName) {
        return positionName.equals(AD_POSITION_NAME_WELFARE_PLAY_LET_TASK) || positionName.equals(AD_POSITION_NAME_WELFARE_MALL_TASK);
    }

    @ReactMethod
    public void kachaShowRedDot(Promise promise) {
        promise.resolve(String.valueOf(KachaRedDotUtil.kachaRedDotShow()));
    }

    @ReactMethod
    public void showRuleDialog(ReadableMap params, final Promise promise) {
        Router.getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                try {
                    Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().showMasterVipAgreementConfirmDialog(new IDataChangeCallback<String>() {
                        @Override
                        public void change(String obj) {
                            if ("ON_CLICK_ACTION_BTN".equalsIgnoreCase(obj)) {
                                promise.resolve(true);
                            }
                        }
                    }, true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }


    @ReactMethod
    public void showFeedBackDialog(ReadableMap params, final Promise promise) {
        final int viewTag = RNUtils.optIntFromRMap(params, "viewTag");
        final String anchorName = RNUtils.optStringFromRMap(params, "anchorName");
        final String dialogType = RNUtils.optStringFromRMap(params, "dialogType");
        final String adInfo = RNUtils.optStringFromRMap(params, "adInfo");
        ReactApplicationContext context = getReactApplicationContext();
        UIManagerModule uiManager = context.getNativeModule(UIManagerModule.class);
        uiManager.addUIBlock(new UIBlock() {
            @Override
            public void execute(NativeViewHierarchyManager nvhm) {
                BaseFragment2 baseFragment2 = RNUtils.queryFragmentByReactContext(getReactApplicationContext());
                View startView = nvhm.resolveView(viewTag);
                if (XmFeedBackPopDialog.DIALOG_TYPE_NORMAL.equals(dialogType)) {
                    XmFeedBackManager.INSTANCE.showFeedBackDialogCommon(null, startView, baseFragment2, anchorName, null, null, new XmFeedBackPopDialog.IOnFeedBackListener() {
                        @Override
                        public void onDialogShow(boolean showSuccess) {
                        }

                        @Override
                        public void onFeedBack(int feedPosition, @NonNull XmFeedInnerModel model) {
                            try {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("name", model.getName());
                                jsonObject.put("codeType", model.getCodeType());
                                jsonObject.put("toast", model.getToast());
                                jsonObject.put("parentName", model.getParentName());
                                jsonObject.put("parentCodeType", model.getParentCodeType());
                                jsonObject.put("parentToast", model.getParentToast());
                                promise.resolve(RNUtils.jsonToReact(jsonObject));
                            } catch (JSONException e) {
                            }
                        }
                    }, false, dialogType);
                } else {
                    Advertis advertis;
                    try{
                        advertis = GsonUtils.parseJson(adInfo, Advertis.class);
                    } catch (Exception e) {
                        e.printStackTrace();
                        CustomToast.showDebugFailToast(e.getMessage());
                        promise.reject(Boolean.FALSE.toString(), e.getMessage());
                        return;
                    }
                    if (advertis == null) {
                        CustomToast.showDebugFailToast("advertis is null");
                        promise.reject(Boolean.FALSE.toString(), "advertis is null");
                        return;
                    }

                    if (!XmAdFeedbackUtil.feedbackEnable(advertis)){
                        CustomToast.showDebugFailToast("ad feedback disable : positionId = " +
                                advertis.getAdPositionId() + ", positionName = " + advertis.getPositionName());
                        promise.reject(Boolean.FALSE.toString(), "ad feedback disable : positionId = " +
                                advertis.getAdPositionId() + ", positionName = " + advertis.getPositionName());
                        return;
                    }

                    final Advertis finalAdvertis = advertis;
                    XmFeedBackManager.INSTANCE.showFeedBackDialogForAd(null, startView,
                            baseFragment2, anchorName, true, true,
                            false, 0, new XmFeedBackPopDialog.IOnFeedBackListener() {
                        @Override
                        public void onDialogShow(boolean showSuccess) {
                        }

                        @Override
                        public void onFeedBack(int feedPosition, @NonNull XmFeedInnerModel model) {
                            try {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("name", model.getName());
                                jsonObject.put("codeType", model.getCodeType());
                                jsonObject.put("toast", model.getToast());
                                jsonObject.put("parentName", model.getParentName());
                                jsonObject.put("parentCodeType", model.getParentCodeType());
                                jsonObject.put("parentToast", model.getParentToast());
                                promise.resolve(RNUtils.jsonToReact(jsonObject));

                                List<XmFeedInnerModel> list = new ArrayList<>();
                                list.add(model);
                                XmAdFeedbackUtil.recordFeedback(finalAdvertis, list);
                            } catch (JSONException e) {
                            }
                        }
                    });
                }
            }
        });
    }

    @ReactMethod
    public void getShowNotesAbValue(final Promise promise) {
        promise.resolve(NewShowNotesManager.INSTANCE.userNewShowNotes());
    }

    @ReactMethod
    public void addToListenTrack(ReadableMap readableMap, final Promise promise) {
        try {
            final double trackId = RNUtils.optDoubleFromRMap(readableMap, "trackId");
            final double albumId = RNUtils.optDoubleFromRMap(readableMap, "albumId");
            final double createdAt = RNUtils.optDoubleFromRMap(readableMap, "createdAt");
            final int duration = RNUtils.optIntFromRMap(readableMap, "duration");
            final String trackCover = RNUtils.optStringFromRMap(readableMap, "trackCover");
            final String trackTitle = RNUtils.optStringFromRMap(readableMap, "trackTitle");
            final String albumTitle = RNUtils.optStringFromRMap(readableMap, "albumTitle");
            final boolean isQuickListen = RNUtils.optBooleanFromRMap(readableMap, "isQuickListen", false);
            Track track = new Track();
            track.setKind(PlayableModel.KIND_TRACK);
            track.setDataId((long) trackId);
            track.setCoverUrlLarge(trackCover);
            track.setTrackTitle(trackTitle);
            track.setCreatedAt((long) createdAt);
            track.setDuration(duration);
            SubordinatedAlbum album = new SubordinatedAlbum();
            album.setAlbumId((long) albumId);
            track.setAlbum(album);
            track.setAlbumTitle(albumTitle);
            ToListenManager.INSTANCE.addToListenTrack(true, !isQuickListen, !isQuickListen, track, null, null, new IDataCallBack<CopyOnWriteArrayList<SimpleTrackForToListen>>() {
                @Override
                public void onSuccess(@Nullable CopyOnWriteArrayList<SimpleTrackForToListen> data) {
                    promise.resolve(java.lang.Boolean.TRUE.toString());
                }

                @Override
                public void onError(int code, String message) {
                    promise.reject(java.lang.Boolean.FALSE.toString(), message);
                }
            }, false);
        } catch (Throwable throwable) {
        }
    }


    @ReactMethod
    public void toBKTrackDetail(ReadableMap params, final Promise promise) {
        final long trackId = Double.valueOf(RNUtils.optDoubleFromRMap(params, "trackId")).longValue();
        final String clickPlayIting = RNUtils.optStringFromRMap(params, "clickPlayIting");
        BaseFragment2 baseFragment2 = RNUtils.queryFragmentByReactContext(getReactApplicationContext());
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().showH5ToListenDialog(trackId, clickPlayIting, NewShowNotesManager.SOURCE_FROM_HOME);
        } catch (Exception e) {
            e.printStackTrace();
        }
        promise.resolve(java.lang.Boolean.TRUE.toString());
    }


    @ReactMethod
    public void getDataCommon(String business, Promise promise) {
        Bundle data = ReactViewDataHolder.getInstance().getData(business);
        if (ConstantsOpenSdk.isDebug) {
            Log.d("BusinessInfoModule", "getDataCommon >>> " + data);
        }
        if (data == null) {
            promise.reject(Boolean.FALSE.toString(),"NoData");
            return;
        }

        WritableMap result = Arguments.fromBundle(data);
        promise.resolve(result);
    }

    private void sendFeedBackEvent(ReactApplicationContext context, JSONObject jsonObject) {
        try {
            RNUtils.sendEvent(context, "onFeedbackClicked", RNUtils.jsonToReact(jsonObject));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sendFeedBackEvent(ReactApplicationContext context, String actionName, String pageIdentify) {
        sendFeedBackEvent(context,actionName,pageIdentify,"");
    }
    private void sendFeedBackEvent(ReactApplicationContext context, String actionName, String pageIdentify, String btnText) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("actionName", actionName);
            jsonObject.put("pageIdentify", pageIdentify);
            jsonObject.put("btnText", btnText);
            sendFeedBackEvent(context, jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @ReactMethod
    public void showNewFeedBackDialog(ReadableMap params, final Promise promise) {
        // https://alidocs.dingtalk.com/i/nodes/o14dA3GK8g5NEKnDUZo4B40jV9ekBD76?cid=58369400783&utm_source=im&utm_scene=person_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=ding51f195092fd77474
        // 1、对声音进行负反馈（会自动在底下添加 “加待播” 入口） 2、对专辑进行负反馈（会自动在底下添加 “订阅” 入口）3、对模块进行负反馈
        // 4、仅显示第二级不喜欢 5、针对书籍进行负反馈 6 短视频负反馈
        final int popViewType = RNUtils.optIntFromRMap(params, "popViewType");
        final int refId = RNUtils.optIntFromRMap(params, "refId");
        final String bizType = RNUtils.optStringFromRMap(params, "bizType");
        // “模块负反馈”的文字
        final String moduleFeedbackTitle = RNUtils.optStringFromRMap(params, "moduleFeedbackTitle");
        // “跳转更多”入口的文字
        final String moreLinkText = RNUtils.optStringFromRMap(params, "moreLinkText");
        // “不喜欢” 入口的文字
        final String dislikeEntranceText = RNUtils.optStringFromRMap(params, "dislikeEntranceText");
        final String dialogType = RNUtils.optStringFromRMap(params, "dialogType");
        final String anchorName = RNUtils.optStringFromRMap(params, "anchorName");
        final String adInfo = RNUtils.optStringFromRMap(params, "adInfo");
        final String customHeaderTitle = RNUtils.optStringFromRMap(params, "customHeaderTitle");
        final String customHeaderSubTitle = RNUtils.optStringFromRMap(params, "customHeaderSubTitle");
        final String customHeaderCover = RNUtils.optStringFromRMap(params, "customHeaderCover");
        final String pageIdentify = RNUtils.optStringFromRMap(params, "pageIdentify");
        final String disLikeAllLive = RNUtils.optStringFromRMap(params, "disLikeAllLive");
        final int shake = RNUtils.optIntFromRMap(params, "shake");
        final int customShowStyle = RNUtils.optIntFromRMap(params, "customShowStyle");
        final int moduleFeedbackIconType = RNUtils.optIntFromRMap(params, "moduleFeedbackIconType");
        final String removeHome = RNUtils.optStringFromRMap(params, "removeHome");
        final boolean isShowAddToListen = RNUtils.optBooleanFromRMap(params, "isShowAddToListen", true);

        // 订阅听单标题
        final String relateTitle = RNUtils.optStringFromRMap(params, "relateTitle");
        // 是否已经订阅
        final boolean isRelated = RNUtils.optBooleanFromRMap(params, "isRelated", false);

        final ReactApplicationContext context = getReactApplicationContext();
        UIManagerModule uiManager = context.getNativeModule(UIManagerModule.class);
        UIBlock uIBlock = new UIBlock() {
            @Override
            public void execute(NativeViewHierarchyManager nvhm) {
                BaseFragment2 baseFragment2 = RNUtils.queryFragmentByReactContext(getReactApplicationContext());
                if (TextUtils.isEmpty(dialogType) || NewXmFeedBackPopDialog.DIALOG_TYPE_NORMAL.equals(dialogType)) {
                    MoreFuncBuild build = null;
                    IMoreFuncListener listener = new IMoreFuncListener() {

                        @Override
                        public void onDialogShow() {
                            sendFeedBackEvent(context, "kXMHomeFeedbackShowIdentify", pageIdentify);
                        }

                        @Override
                        public void onCancelClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackCancelIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onTopModuleClick() {
                            String actionName = "";
                            if (popViewType == 1 || popViewType == 2) {
                                actionName = "kXMHomeFeedbackHeaderIdentify";
                            } else if (popViewType == 3 || popViewType == 5 || popViewType == 6) {
                                actionName = "kXMHomeFeedbackCustomHeaderIdentify";
                            } else {
                                actionName = "other";
                            }
                            sendFeedBackEvent(context, actionName, pageIdentify);

                        }

                        @Override
                        public void onTopSubTitleClick() {
                            sendFeedBackEvent(context, "kXMHomeFeedbackHeaderDescIdentify", pageIdentify);
                        }

                        @Override
                        public void onMoreClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackMoreIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onLevel1DisLikeClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackModuleIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onAddToListenClick(boolean isAdd, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackTobeIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onSubscriptionClick(boolean isSubscription, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackSubscribeIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onAddToBookList(boolean isAddToBookList, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackAddShelfIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onRemoveFromBookList(boolean isAddToBookList, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackRemoveShelfIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onLevel2DisLikeClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackMoreDislikeIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void disLikeAllLive(@Nullable String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackRemoveAllLiveIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void removeItem(@Nullable String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackRemoveHomeIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onCommonItemClick(@NonNull String commonItemText) {
                            if (!TextUtils.isEmpty(relateTitle)) {
                                sendFeedBackEvent(context, "kXMHomeFeedbackRelateIdentify", pageIdentify, commonItemText);
                            }
                        }

                    };
                    NewXmFeedBackPopDialog.IOnFeedBackListener feedBackListener =  new NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        @Override
                        public void onDialogShow(boolean showSuccess) {
                        }

                        @Override
                        public void onFeedBack(List<XmFeedInnerModel> list) {
                            try {
                                JSONObject result = new JSONObject();
                                result.put("actionName", "kXMHomeFeedbackDislikeOptionIdentifyNew");
                                result.put("pageIdentify", pageIdentify);

                                if (list != null && !list.isEmpty()) {
                                    JSONArray array = new JSONArray();
                                    for (XmFeedInnerModel model : list) {
                                        JSONObject jsonObject = new JSONObject();
                                        jsonObject.put("name", model.getName());
                                        jsonObject.put("codeType", model.getCodeType());
                                        jsonObject.put("toast", model.getToast());
                                        jsonObject.put("parentName", model.getParentName());
                                        jsonObject.put("parentCodeType", model.getParentCodeType());
                                        jsonObject.put("parentToast", model.getParentToast());


                                        JSONObject reportProp = new JSONObject();
                                        reportProp.put("strategy_type", model.getDisposalType());
                                        reportProp.put("label", model.getName());
                                        reportProp.put("label_id", model.getLabelId());
                                        reportProp.put("label_level", model.getLabelLevel());
                                        jsonObject.put("reportProp", reportProp);
                                        array.put(jsonObject);
                                    }
                                    result.put("feedbackList", array);
                                }

                                sendFeedBackEvent(context, result);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    };
                    DisLikeLeve2Build leve2Build = DisLikeLeve2Build.createCommonBuild(false, anchorName, feedBackListener);
                    leve2Build.setShowToastInner(false);
                    if (popViewType == 1) {
                        build = MoreFuncBuild.createTrackLongClickModel(baseFragment2, refId, listener, false, leve2Build);
                    } else if (popViewType == 2) {
                        build = MoreFuncBuild.createAlbumLongClickModel(baseFragment2, refId, listener, false, leve2Build);
                    } else if (popViewType == 3) {
                        build = MoreFuncBuild.createSocialListenMoreModel(baseFragment2, false, moduleFeedbackTitle, listener, moreLinkText);
                    } else if (popViewType == 4) {
                        build = MoreFuncBuild.createOnlyDisLikeLeve2Model(baseFragment2, listener, leve2Build);
                    } else if (popViewType == 5) {
                        build = MoreFuncBuild.createBookLongClickModel(baseFragment2, refId, listener, false, leve2Build);
                    } else if (popViewType == 6) {
                        build = MoreFuncBuild.createShortVideoLongClickModel(baseFragment2, (long) refId, bizType, listener, true, leve2Build);
                    }

                    if (build != null && popViewType != 4) {
                        build.setShowMore(!TextUtils.isEmpty(moreLinkText));
                        build.setShowMoreText(moreLinkText);

                        build.setShowLevel1Dislike(!TextUtils.isEmpty(moduleFeedbackTitle));
                        build.setLevel1DisLikeTitle(moduleFeedbackTitle);

                        build.setShowLevel2Dislike(!TextUtils.isEmpty(dislikeEntranceText));
                        build.setLevel2DisLikeTitle(dislikeEntranceText);

                        if (!TextUtils.isEmpty(customHeaderTitle)) {
                            MoreTitleModel titleModel = new MoreTitleModel();
                            titleModel.setTitle(customHeaderTitle);
                            titleModel.setSubTitle(customHeaderSubTitle);
                            titleModel.setCoverUrl(customHeaderCover);
                            if (customShowStyle > 0) {
                                titleModel.setShowStyle(customShowStyle);
                            }
                            build.setTitleModel(titleModel);
                        }

                        build.setDisLikeAllLive(disLikeAllLive);
                        build.setShowAddToListen(isShowAddToListen);

                        if (!TextUtils.isEmpty(removeHome)) {
                            build.setRemoveItemText(removeHome);
                            build.setShowRemoveItem(true);
                        }
                    }

                    if (build != null) {
                        boolean vibratorEnable = shake == 1;
                        build.setVibratorEnable(vibratorEnable);
                        leve2Build.setVibratorEnable(vibratorEnable);
                        build.setShowLevel1DisLikeToast(false);

                        if (moduleFeedbackIconType == 1) {
                            build.setLevel1DisLikeIcon(com.ximalaya.ting.android.host.R.drawable.host_ic_more_func_dislike_all_live);
                        }

                        if (!TextUtils.isEmpty(relateTitle)) {
                            if (isRelated) {
                                build.setCommonItemIcon(com.ximalaya.ting.android.host.R.drawable.host_ic_more_func_subscribed);
                            } else {
                                build.setCommonItemIcon(com.ximalaya.ting.android.host.R.drawable.host_ic_more_func_subscription);
                            }
                            build.setCommonItemText(relateTitle);
                        }

                        XmMoreFuncManager.checkShowMorePage(build);
                    }
                } else {
                    Advertis advertis = null;
                    try{
                        advertis = GsonUtils.parseJson(adInfo, Advertis.class);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    MoreFuncBuild build = null;
                    IMoreFuncListener listener = new IMoreFuncListener() {

                        @Override
                        public void onDialogShow() {
                            sendFeedBackEvent(context, "kXMHomeFeedbackShowIdentify", pageIdentify);
                        }

                        @Override
                        public void onCancelClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackCancelIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onTopModuleClick() {
                            String actionName = "";
                            if (popViewType == 1 || popViewType == 2) {
                                actionName = "kXMHomeFeedbackHeaderIdentify";
                            } else if (popViewType == 3 || popViewType == 5) {
                                actionName = "kXMHomeFeedbackCustomHeaderIdentify";
                            } else {
                                actionName = "other";
                            }
                            sendFeedBackEvent(context, actionName, pageIdentify);

                        }

                        @Override
                        public void onTopSubTitleClick() {
                            sendFeedBackEvent(context, "kXMHomeFeedbackHeaderDescIdentify", pageIdentify);
                        }

                        @Override
                        public void onMoreClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackMoreIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onLevel1DisLikeClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackModuleIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onAddToListenClick(boolean isAdd, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackTobeIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onSubscriptionClick(boolean isSubscription, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackSubscribeIdentify", pageIdentify, btnText);
                        }


                        @Override
                        public void onAddToBookList(boolean isAddToBookList, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackAddShelfIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onRemoveFromBookList(boolean isAddToBookList, String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackRemoveShelfIdentify", pageIdentify, btnText);
                        }

                        @Override
                        public void onLevel2DisLikeClick(String btnText) {
                            sendFeedBackEvent(context, "kXMHomeFeedbackMoreDislikeIdentify", pageIdentify, btnText);
                        }
                    };
                    final Advertis finalAdvertis = advertis;
                    NewXmFeedBackPopDialog.IOnFeedBackListener feedBackListener =  new NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        @Override
                        public void onDialogShow(boolean showSuccess) {
                        }

                        @Override
                        public void onFeedBack(List<XmFeedInnerModel> list) {
                            try {
                                JSONObject result = new JSONObject();
                                result.put("actionName", "kXMHomeFeedbackDislikeOptionIdentifyNew");
                                result.put("pageIdentify", pageIdentify);

                                if (list != null && !list.isEmpty()) {
                                    JSONArray array = new JSONArray();
                                    for (XmFeedInnerModel model : list) {
                                        JSONObject jsonObject = new JSONObject();
                                        jsonObject.put("name", model.getName());
                                        jsonObject.put("codeType", model.getCodeType());
                                        jsonObject.put("toast", model.getToast());
                                        jsonObject.put("parentName", model.getParentName());
                                        jsonObject.put("parentCodeType", model.getParentCodeType());
                                        jsonObject.put("parentToast", model.getParentToast());


                                        JSONObject reportProp = new JSONObject();
                                        reportProp.put("strategy_type", model.getDisposalType());
                                        reportProp.put("label", model.getName());
                                        reportProp.put("label_id", model.getLabelId());
                                        reportProp.put("label_level", model.getLabelLevel());
                                        jsonObject.put("reportProp", reportProp);
                                        array.put(jsonObject);
                                    }
                                    result.put("feedbackList", array);
                                }

                                sendFeedBackEvent(context, result);

                                if (XmAdFeedbackUtil.feedbackEnable(finalAdvertis)) {
                                    XmAdFeedbackUtil.recordFeedback(finalAdvertis, list);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    };
                    DisLikeLeve2Build leve2Build = DisLikeLeve2Build.createCommonBuild(false, anchorName, feedBackListener);
                    leve2Build.setShowToastInner(false);
                    leve2Build.setFromAd(true);
                    if (popViewType == 1) {
                        build = MoreFuncBuild.createTrackLongClickModel(baseFragment2, refId, listener, false, leve2Build);
                    } else if (popViewType == 2) {
                        build = MoreFuncBuild.createAlbumLongClickModel(baseFragment2, refId, listener, false, leve2Build);
                    } else if (popViewType == 3) {
                        build = MoreFuncBuild.createSocialListenMoreModel(baseFragment2, false, moduleFeedbackTitle, listener, moreLinkText);
                    } else if (popViewType == 4) {
                        build = MoreFuncBuild.createOnlyDisLikeLeve2Model(baseFragment2, listener, leve2Build);
                    }

                    if (build != null && popViewType != 4) {
                        build.setShowMore(!TextUtils.isEmpty(moreLinkText));
                        build.setShowMoreText(moreLinkText);

                        build.setShowLevel1Dislike(!TextUtils.isEmpty(moduleFeedbackTitle));
                        build.setLevel1DisLikeTitle(moduleFeedbackTitle);

                        build.setShowLevel2Dislike(!TextUtils.isEmpty(dislikeEntranceText));
                        build.setLevel2DisLikeTitle(dislikeEntranceText);

                        if (!TextUtils.isEmpty(customHeaderTitle)) {
                            MoreTitleModel titleModel = new MoreTitleModel();
                            titleModel.setTitle(customHeaderTitle);
                            titleModel.setSubTitle(customHeaderSubTitle);
                            titleModel.setCoverUrl(customHeaderCover);
                            build.setTitleModel(titleModel);
                        }
                    }

                    if (build != null) {
                        boolean vibratorEnable = shake == 1;
                        build.setVibratorEnable(vibratorEnable);
                        leve2Build.setVibratorEnable(vibratorEnable);
                        build.setShowLevel1DisLikeToast(false);
                        XmMoreFuncManager.checkShowMorePage(build);
                    }
                }
            }
        };
        uiManager.addUIBlock(uIBlock);
    }

    @ReactMethod
    public void isAppInstalled(ReadableMap readableMap, final Promise promise) {
        if (readableMap == null || promise == null) {
            return;
        }
        JSONArray jsonArray = new JSONArray();
        ReadableArray appInfoList = readableMap.getArray("appInfoList");
        if (appInfoList != null && appInfoList.size() > 0) {
            boolean installed;
            for (Object packageName : appInfoList.toArrayList()) {
                if (packageName instanceof String) {
                    installed = PackageUtil.isAppInstalled(getCurrentActivity(), (String) packageName);
                } else {
                    installed = false;
                }
                jsonArray.put(installed ? 1 : 0);
            }
        }
        WritableMap map = Arguments.createMap();
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("result", jsonArray);
            map = RNUtils.jsonToReact(jsonObject);
        } catch (Throwable e){
            e.printStackTrace();
        }
        promise.resolve(map);
    }


    /**
     * 页面是否可以全屏左滑关闭,需要页面本身创建的时候有参数canSlide=1
     *
     * @param canSlide 是否可以全屏左滑关闭
     */
    @ReactMethod
    public void setFullSlideAble(final boolean canSlide) {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                Fragment curFragment = RNUtils.queryFragmentByReactContext(getReactApplicationContext());
                if (curFragment instanceof BaseFragment2) {
                    ((BaseFragment2) curFragment).setFullSlideAble(canSlide);
                }
            }
        });
    }

    @ReactMethod
    public void getMyListenAbValue(final Promise promise) {
        promise.resolve(MyListenAbUtil.INSTANCE.getABType());
    }

    @ReactMethod
    public void getUnreadMessageCount(final Promise promise) {
        NoReadModel noReadModel = NoReadManage.getInstance(BaseApplication.getMyApplicationContext()).getNoReadModel();
        int unreadCount = noReadModel != null ? noReadModel.getUnReadMessageCount() : 0;
        promise.resolve(unreadCount);
    }

    @ReactMethod
    public void getMyListenSlideAbValue(final Promise promise) {
        promise.resolve(MyListenAbUtil.INSTANCE.getSlideExperienceFlag());
    }

    @ReactMethod
    public void getFreeFlowValue(final Promise promise) {
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        promise.resolve(freeFlowService != null ? freeFlowService.isOrderFlowPackage() : false);
    }

    @ReactMethod
    public void isVipNewStyle(final Promise promise) {
        boolean isVipNewStyle = false;
        try {
            isVipNewStyle = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().isMineTopVipNewStyle();
        } catch (Exception e) {
            e.printStackTrace();
        }
        promise.resolve(isVipNewStyle);
    }

    @ReactMethod
    public void openLink(String linkUrl, final Promise promise) {
        if (TextUtils.isEmpty(linkUrl)) {
            return;
        }
        String MOBILE_FREE_FLOW = "replace_mobile_freeflow_param";
        String UNICOME_FREE_FLOW = "replace_unicom_freeflow_param";
        String TELECOM_FREE_FLOW = "replace_telecom_freeflow_param";
        if (!linkUrl.contains(MOBILE_FREE_FLOW) && !linkUrl.contains(UNICOME_FREE_FLOW) && !linkUrl.contains(TELECOM_FREE_FLOW)) {
            handleUrlClick(linkUrl);
            return;
        }
        String freeFlowParams = "";
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        if (freeFlowService != null) {
            freeFlowParams = freeFlowService.getFreeFlowParams();
        }
        if (TextUtils.isEmpty(freeFlowParams)) {
            handleUrlClick(linkUrl);
            return;
        }
        String url = "";
        if (linkUrl.contains(MOBILE_FREE_FLOW)) {
            url = linkUrl.replace(MOBILE_FREE_FLOW, freeFlowParams);
        } else if (linkUrl.contains(UNICOME_FREE_FLOW)) {
            url = linkUrl.replace(UNICOME_FREE_FLOW, freeFlowParams);
        } else if (linkUrl.contains(TELECOM_FREE_FLOW)) {
            url = linkUrl.replace(TELECOM_FREE_FLOW, freeFlowParams);
        }
        handleUrlClick(url);
    }

    private void handleUrlClick(String url) {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            ToolUtil.clickUrlAction((MainActivity) activity, url.trim(), null);
        }
    }

    @ReactMethod
    public void getNetworkTypeOperator(final Promise promise) {
        int cOper = -1;
        switch (NetworkType.getOperator(BaseApplication.getMyApplicationContext())) {
            case NetworkType.OPERATOR_CMCC : {
                cOper = 0;
            }
            case NetworkType.OPERATOR_UNICOME : {
                cOper = 1;
            }
            case NetworkType.OPERATOR_TELECOM : {
                cOper = 1;
            }
            default:  {
                cOper = -1;
            }
        }
        promise.resolve(cOper);
    }

    @ReactMethod
    public void getIsLocalListenedAlbum(int albumId, final Promise promise) {
        Track track = XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).getLastPlayTrackInAlbum(albumId);
        promise.resolve(track != null);
    }

    private void doRewardCoinRequest(final int rewardCoins, final int rewardType, final JssdkFuliSuperCommonModel superCommonModel,
                                     final Advertis advertis, final IAdModel adModel,
                                     final String requestId, final int retryCount, final boolean realFinishTask, final IRequestRewardCoinCallback callback) {
        JSONObject parmasMap = new JSONObject();
        try {
            long ts = System.currentTimeMillis();
            parmasMap.put("requestId", requestId);
            int encryptType = 1;
            String ecpm = "-1";
            if (advertis != null) {
                parmasMap.put("adId", advertis.getAdid());
                parmasMap.put("adResponseId", advertis.getResponseId());
                encryptType = AdManager.getEncryptType(advertis);
                ecpm = AdManager.getEcpm(advertis);
            } else if (adModel != null) {
                parmasMap.put("adId", adModel.getAdid());
                parmasMap.put("adResponseId", adModel.getResponseId());
                encryptType = adModel.getEncryptType();
                ecpm = adModel.getEcpm();
            }
            parmasMap.put("ecpm", ecpm);
            parmasMap.put("encryptType", encryptType);
            parmasMap.put("fallbackReq", realFinishTask ? 0 : 1);
            parmasMap.put("ts", ts);
            parmasMap.put("coins", rewardCoins);
            parmasMap.put("retry", retryCount);
            parmasMap.put("sourceName", superCommonModel.sourceName);
            parmasMap.put("rewardType", rewardType);
            String ticketStr = XuidManager.INSTANCE.getTicket("b=incentives&s=gold_reward&u=" + UserInfoMannage.getUid());
            parmasMap.put("ticket", ticketStr);
            String baseSignature = requestId + "&" + UserInfoMannage.getUid() + "&" + rewardType + "&" + rewardCoins + "&" +
                    ticketStr + "&" + ecpm + "&" + encryptType + "&" + ts + "&" + retryCount + "&" + KEY;
            String signatureStr = MD5.md5(baseSignature);
            parmasMap.put("signature", signatureStr);
            String extInfoStr = "";
            if (superCommonModel != null) {
                extInfoStr = superCommonModel.extInfo;
            }
            parmasMap.put("extMap", extInfoStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        AdRequest.getIncentiveRewardGoldCoin(parmasMap, new IDataCallBack<AdGoldCoinResponseData>() {
            @Override
            public void onSuccess(@Nullable AdGoldCoinResponseData rewardResponse) {
                if (rewardResponse != null && rewardResponse.success) {
                    if (callback != null) {
                        callback.onSuccess(rewardResponse);
                    }
                } else {
                    if (rewardResponse != null && rewardResponse.retry && retryCount <= 1) {
                        doRewardCoinRequest(rewardCoins, rewardType, superCommonModel, advertis, adModel, requestId, retryCount + 1, realFinishTask, callback);
                    } else {
                        if (rewardResponse != null && !TextUtils.isEmpty(rewardResponse.toast)) {
                            CustomToast.showFailToast(rewardResponse.toast);
                        } else {
                            CustomToast.showFailToast("网络异常，请稍后重试");
                        }
                        if (callback != null) {
                            callback.onFail(rewardResponse != null && !TextUtils.isEmpty(rewardResponse.toast) ? rewardResponse.toast : "data异常");
                        }
                    }
                }
            }

            @Override
            public void onError(int errorCode, String errorMessage) {
                if (retryCount <= 1) {
                    doRewardCoinRequest(rewardCoins, rewardType, superCommonModel, advertis, adModel, requestId, retryCount + 1, realFinishTask, callback);
                } else {
                    CustomToast.showFailToast("网络异常，请稍后重试");
                    if (callback != null) {
                        callback.onFail(errorMessage);
                    }
                }
            }
        });
    }

    @ReactMethod
    public void anchorFollow(ReadableMap readableMap, final Promise promise) {
        final long anchorId = (long) RNUtils.optDoubleFromRMap(readableMap, "anchorId");
        final boolean isFollow = RNUtils.optBooleanFromRMap(readableMap, "isFollow", false);
        final int bizType = RNUtils.optIntFromRMap(readableMap, "bizType");
        final int subBizType = RNUtils.optIntFromRMap(readableMap, "subBizType");
        final long albumId = (long) RNUtils.optDoubleFromRMap(readableMap, "albumId");
        final String srcPage = RNUtils.optStringFromRMap(readableMap, "srcPage");
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction()
                    .doAnchorFollowFromRnCommon(anchorId,
                            albumId,
                            isFollow,
                            bizType,
                            subBizType, srcPage, new IDataCallBack<Boolean>() {
                                @Override
                                public void onSuccess(@Nullable Boolean data) {
                                    if (data == true) {
                                        promise.resolve(true);
                                    } else {
                                        promise.resolve(false);
                                    }
                                }

                                @Override
                                public void onError(int code, String message) {
                                    promise.resolve(false);
                                }
                            });
        } catch (Exception e) {
            promise.resolve(false);
        }
    }

    @ReactMethod
    public void inviteWechatFriends() {
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().inviteWechatFriends();
        } catch (Exception e) {
        }
    }

    @ReactMethod
    public void getMobileAddressBookList(final Promise promise) {
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getMobileAddressBookList(new IDataCallBack<JSONObject>() {
                @Override
                public void onSuccess(@Nullable JSONObject data) {
                    try {
                        promise.resolve(RNUtils.jsonToReact(data));
                    } catch (JSONException e) {
                    }
                }

                @Override
                public void onError(int code, String message) {

                }
            });
        } catch (Exception e) {
        }
    }

    public interface IRequestRewardCoinCallback {
        void onSuccess(AdGoldCoinResponseData responseData);
        void onFail(String message);
    }

    @ReactMethod
    public void isInToListenTrack(ReadableMap readableMap, Promise promise) {
        try {
            long trackId = (long) RNUtils.optDoubleFromRMap(readableMap, "trackId");
            long albumId = (long) RNUtils.optDoubleFromRMap(readableMap, "albumId");

            if (trackId > 0 && ToListenManager.INSTANCE.getSingleToListenTrackSync(trackId) != null) {
                promise.resolve(true);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (promise != null) {
            promise.resolve(false);
        }
    }

    @ReactMethod
    public void trackCollect(final ReadableMap readableMap, final Promise promise) {
        HandlerManager.postOnMainAuto(new Runnable() {
            @Override
            public void run() {
                try {
                    long trackId = (long) RNUtils.optDoubleFromRMap(readableMap, "trackId");
                    boolean isCollect = RNUtils.optBooleanFromRMap(readableMap, "isCollect", false);
                    final boolean showToast = RNUtils.optBooleanFromRMap(readableMap, "showToast", true);
                    TrackCollectManager.getInstance().requestCollectOrUnCollect(isCollect, trackId, new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean data) {
                            promise.resolve(true);
                        }

                        @Override
                        public void onError(int code, String message) {
                            promise.resolve(false);
                            if (showToast) {
                                CustomToast.showFailToast(TextUtils.isEmpty(message) ? "请稍后再试" : message);
                            }
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @ReactMethod
    public void shareTrack(final ReadableMap readableMap, final Promise promise) {
        HandlerManager.postOnMainAuto(new Runnable() {
            @Override
            public void run() {
                try {
                    final long trackId = (long) RNUtils.optDoubleFromRMap(readableMap, "trackId");
                    final long albumId = (long) RNUtils.optDoubleFromRMap(readableMap, "albumId");
                    final String themeColor = RNUtils.optStringFromRMap(readableMap, "themeColor");
                    final String curPage = RNUtils.optStringFromRMap(readableMap, "currPage");
                    final int subType = RNUtils.optIntFromRMap(readableMap, "subType");

                    Map<String, String> params = new HashMap<>();
                    params.put("device", "android");
                    params.put("trackId", trackId + "");
                    CommonRequestM.getTrackInfoDetail(params, new IDataCallBack<TrackM>() {
                        @Override
                        public void onSuccess(@Nullable TrackM trackM) {
                            if (trackM == null) {
                                promise.reject(Boolean.FALSE.toString(), "data is null");
                                return;
                            }

                            int type = ICustomShareContentType.SHARE_TYPE_TRACK;
                            int sharePanelType = SharePanelType.FUNCTION_3;
                            ShareWrapContentModel contentModel = new ShareWrapContentModel(
                                    sharePanelType, curPage, type);
                            contentModel.soundInfo = trackM;
                            contentModel.mShowSuccessDialog = false;
                            contentModel.showFamilyInfo = true;
                            contentModel.isShowPosterHead = true;
                            contentModel.paramSubType = subType;

                            if (!TextUtils.isEmpty(themeColor)) {
                                try {
                                    contentModel.panelColor = Color.parseColor(themeColor);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                            contentModel.isFromPlayPage = true;
                            contentModel.fromPage = curPage;
                            contentModel.trackForm = QuickListenTabAbManager.QUICK_LISTEN_BUNDLE_NAME;
                            contentModel.mShareAdRequestParams =
                                    new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_SOUND,
                                            trackM.getDataId() + "");

                            if (!TextUtils.isEmpty(themeColor)) {
                                try {
                                    contentModel.playPageColor = Color.parseColor(themeColor);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                            new ShareManager(MainApplication.getMainActivity(), contentModel, new ShareManager.Callback() {
                                @Override
                                public void onShare(AbstractShareType shareType) {

                                }
                            }).showSharePanelDialog();

                            promise.resolve("");
                        }

                        @Override
                        public void onError(int code, String message) {
                            promise.reject(Boolean.FALSE.toString(), message);
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    promise.reject(e);
                }
            }
        });
    }
}
