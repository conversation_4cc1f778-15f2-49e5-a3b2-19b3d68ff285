package com.ximalaya.ting.android.host.manager.ad.videoad;

import android.app.Dialog;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.framework.autosize.AutoSizeCompat;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.BusinessExtraInfo;

/**
 * 激励视频点击弹窗
 */
public class VideoAdClickDialogFragment extends BaseDialogFragment implements View.OnClickListener {

    private static final String ARG_POPUP_INFO = "popup_info";

    private Advertis mAdvertis;
    private BusinessExtraInfo.IncentivePopupInfo mPopupInfo;
    private boolean mIsFromContinue;
    private int mTipStayTime;
    private IDialogCallback mCallback;

    // UI组件
    private ImageView mTopImageView;
    private ImageView mLightEffectView;
    private TextView mTitleTextView;
    private ViewGroup mMaterialContainer;
    private ImageView mMaterialIconView;
    private TextView mMaterialTitleView;
    private TextView mMaterialDescriptionView;
    private TextView mButtonView;
    private TextView mCountdownTextView;
    private ImageView mCloseImageView;

    // 倒计时器
    private CanPauseCountDownTimer mCountDownTimer;

    public interface IDialogCallback {
        void onButtonClick();

        void onClose(boolean isAuto);
    }

    public static VideoAdClickDialogFragment newInstance(Advertis advertis, BusinessExtraInfo.IncentivePopupInfo popupInfo, IDialogCallback callback, int tipStayTime, boolean isFromContinue) {
        VideoAdClickDialogFragment fragment = new VideoAdClickDialogFragment();
        Bundle args = new Bundle();
        fragment.mAdvertis = advertis;
        fragment.mPopupInfo = popupInfo;
        fragment.mIsFromContinue = isFromContinue;
        fragment.mTipStayTime = tipStayTime;
        fragment.setArguments(args);
        fragment.mCallback = callback;
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            Window window = dialog.getWindow();
            if (window != null) {
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                window.setWindowAnimations(-1);
            }
        }
        View view = inflater.inflate(R.layout.host_video_ad_click_dialog_layout, container, false);
        initViews(view);
        setupData(view);
        startCountdown();
        return view;
    }

    private void initViews(View view) {
        mTopImageView = view.findViewById(R.id.host_iv_top_image);
        mLightEffectView = view.findViewById(R.id.host_light_effect);
        mTitleTextView = view.findViewById(R.id.host_tv_title);
        mMaterialContainer = view.findViewById(R.id.host_material_container);
        mMaterialIconView = view.findViewById(R.id.host_iv_material_icon);
        mMaterialTitleView = view.findViewById(R.id.host_tv_material_title);
        mMaterialDescriptionView = view.findViewById(R.id.host_tv_material_description);
        mButtonView = view.findViewById(R.id.host_tv_button);
        mCountdownTextView = view.findViewById(R.id.host_tv_countdown);
        mCloseImageView = view.findViewById(R.id.host_close_iv);

        view.setOnClickListener(this);
        mButtonView.setOnClickListener(this);
        mCloseImageView.setOnClickListener(this);
    }

    private void setupData(View view) {
        if (mPopupInfo == null || mAdvertis == null) {
            return;
        }

        if (mIsFromContinue) {
            view.setBackground(null);
            ViewGroup.MarginLayoutParams buttonParams = (ViewGroup.MarginLayoutParams) mButtonView.getLayoutParams();
            buttonParams.setMargins(buttonParams.leftMargin, BaseUtil.dp2px(getContext(), 26), buttonParams.rightMargin, buttonParams.bottomMargin);
            mButtonView.setLayoutParams(buttonParams);
            mMaterialContainer.setVisibility(View.GONE);
            String description = String.format("再体验 %s秒 即可获得奖励", mTipStayTime);
            setTitleWithHighlight(mTitleTextView, description, mTipStayTime);
            mButtonView.setText("继续体验");
            mCountdownTextView.setText("放弃体验，继续看视频");
            mCountdownTextView.setOnClickListener(this);
            return;
        }
        mMaterialContainer.setVisibility(View.VISIBLE);

        // 设置顶部标题图片
        if (!TextUtils.isEmpty(mPopupInfo.getPopupTitle())) {
            ImageManager.Options options = new ImageManager.Options();
            options.targetWidth = BaseUtil.dp2px(getContext(), 311);
            ImageManager.from(getContext()).downloadBitmap(mPopupInfo.getPopupTitle(), options, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    if (bitmap != null && mTopImageView != null) {
                        mTopImageView.setImageBitmap(bitmap);
                        mTopImageView.setVisibility(View.VISIBLE);
                    }
                }
            });
        }

        // 设置标题文案
        String description = String.format(mPopupInfo.getPopupDescription(), mTipStayTime);
        setTitleWithHighlight(mTitleTextView, description, mTipStayTime);

        // 设置素材图标
        if (!TextUtils.isEmpty(mAdvertis.getLogoUrl())) {
            ImageManager.from(getContext()).displayImage(mMaterialIconView, mAdvertis.getLogoUrl(), -1,
                    BaseUtil.dp2px(getContext(), 36), BaseUtil.dp2px(getContext(), 36));
        }

        // 设置素材标题
        if (!TextUtils.isEmpty(mAdvertis.getName())) {
            mMaterialTitleView.setText(mAdvertis.getName());
        }

        // 设置素材描述
        if (!TextUtils.isEmpty(mAdvertis.getDescription())) {
            mMaterialDescriptionView.setText(mAdvertis.getDescription());
        }

        // 设置按钮文案
        if (!TextUtils.isEmpty(mPopupInfo.getButtonText())) {
            mButtonView.setText(mPopupInfo.getButtonText());
        }

        // 启动光效旋转动画
        if (mLightEffectView != null) {
            mLightEffectView.setVisibility(View.VISIBLE);
            mLightEffectView.setRotation(0f);
            android.animation.ObjectAnimator animator = android.animation.ObjectAnimator.ofFloat(mLightEffectView, "rotation", 0f, 360f);
            animator.setDuration(3000); // 3秒
            animator.setInterpolator(new android.view.animation.LinearInterpolator());
            animator.start();
        }
    }

    /**
     * 设置标题文案，并高亮"xx秒"为红色#FFFF4444
     */
    private void setTitleWithHighlight(TextView textView, String description, int tipStayTime) {
        String highlight = tipStayTime + "秒";
        int start = description.indexOf(highlight);
        if (start >= 0) {
            SpannableString spannable = new SpannableString(description);
            spannable.setSpan(
                new ForegroundColorSpan(Color.parseColor("#FFFF4444")),
                start,
                start + highlight.length(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            );
            textView.setText(spannable);
        } else {
            textView.setText(description);
        }
    }

    private void startCountdown() {
        if (mPopupInfo == null || mPopupInfo.getAutoCloseDelay() <= 0 || mIsFromContinue) {
            return;
        }

        int autoCloseDelay = mPopupInfo.getAutoCloseDelay();
        mCountdownTextView.setText(autoCloseDelay + "s后自动放弃");
        mCountDownTimer = new CanPauseCountDownTimer(autoCloseDelay * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int seconds = (int) (millisUntilFinished / 1000) + 1;
                if (mCountdownTextView != null) {
                    if (seconds < 10) {
                        mCountdownTextView.setText("0"+ seconds + "s后自动放弃");
                    } else {
                        mCountdownTextView.setText(seconds + "s后自动放弃");
                    }
                }
            }

            @Override
            public void onFinish() {
                if (mCallback != null) {
                    mCallback.onClose(true);
                }
                dismiss();
            }
        };
        mCountDownTimer.start();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mCountDownTimer != null) {
            mCountDownTimer.resume();
        }
    }

    public void onPause() {
        super.onPause();
        if (mCountDownTimer != null) {
            mCountDownTimer.pause();
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.host_tv_button) {
            // 点击按钮
            if (mCallback != null) {
                mCallback.onButtonClick();
            }
            dismiss();
        } else {
            if (mCallback != null) {
                mCallback.onClose(false);
            }
            dismiss();
        }
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window window = dialog.getWindow();
            if (window != null) {
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
    }
}
